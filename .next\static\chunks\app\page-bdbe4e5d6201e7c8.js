(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{285:(e,s,r)=>{"use strict";r.d(s,{$:()=>i});var t=r(5155),a=r(2115),l=r(9434);let i=a.forwardRef((e,s)=>{let{className:r,variant:a="default",size:i="default",...n}=e;return(0,t.jsx)("button",{className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"}[a],{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[i],r),ref:s,...n})});i.displayName="Button"},5226:(e,s,r)=>{"use strict";r.d(s,{ProductGrid:()=>y});var t=r(5155),a=r(2115),l=r(6766),i=r(9946);let n=(0,i.A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);var c=r(1976);let o=(0,i.A)("leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]]);var d=r(9434);let m=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,d.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});m.displayName="Card",a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",r),...a})}).displayName="CardHeader",a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("h3",{ref:s,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})}).displayName="CardTitle",a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("p",{ref:s,className:(0,d.cn)("text-sm text-muted-foreground",r),...a})}).displayName="CardDescription";let x=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,d.cn)("p-6 pt-0",r),...a})});x.displayName="CardContent",a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,d.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter";var u=r(6126),g=r(285);function p(e){let s,{product:r,onQuickAdd:i,onViewDetails:p}=e,[f,h]=(0,a.useState)(r.images[0]),[b,v]=(0,a.useState)(null),[y,j]=(0,a.useState)(!1),N=e=>{h(e),v(e)},w=()=>{b&&(h(r.images[0]),v(null))};return(0,t.jsxs)(m,{className:"group relative overflow-hidden transition-all duration-300 hover:shadow-lg",children:[(0,t.jsx)(g.$,{variant:"ghost",size:"icon",className:"absolute top-2 right-2 z-10 bg-white/80 backdrop-blur-sm hover:bg-white",onClick:()=>j(!y),children:(0,t.jsx)(c.A,{className:(0,d.cn)("w-4 h-4 transition-colors",y?"fill-red-500 text-red-500":"text-gray-600")})}),r.ecoRating&&(0,t.jsxs)(u.E,{className:(0,d.cn)("absolute top-2 left-2 z-10 text-white",(0,d.sj)(r.ecoRating)),children:[(0,t.jsx)(o,{className:"w-3 h-3 mr-1"}),r.ecoRating]}),(0,t.jsxs)("div",{className:"relative aspect-square overflow-hidden",children:[(0,t.jsx)(l.default,{src:f,alt:r.name,fill:!0,className:"object-cover transition-transform duration-300 group-hover:scale-105",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center p-4",children:(0,t.jsx)(g.$,{onClick:()=>null==i?void 0:i(r),className:"w-full bg-white text-black hover:bg-gray-100",children:"Quick Add"})})]}),(0,t.jsxs)(x,{className:"p-4",children:[r.brand&&(0,t.jsx)("p",{className:"text-xs text-muted-foreground uppercase tracking-wide mb-1",children:r.brand}),(0,t.jsx)("h3",{className:"font-semibold text-sm mb-2 line-clamp-2 cursor-pointer hover:text-primary transition-colors",onClick:()=>null==p?void 0:p(r),children:r.name}),(0,t.jsxs)("div",{className:"flex items-center gap-1 mb-2",children:[(0,t.jsx)("div",{className:"flex",children:(s=r.rating,Array.from({length:5},(e,r)=>(0,t.jsx)(n,{className:(0,d.cn)("w-4 h-4",r<Math.floor(s)?"fill-yellow-400 text-yellow-400":"text-gray-300")},r)))}),(0,t.jsxs)("span",{className:"text-xs text-muted-foreground ml-1",children:["(",r.reviews,")"]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsx)("span",{className:"text-lg font-bold",children:(0,d.$g)(r.price)}),r.carbonFootprint&&(0,t.jsxs)("span",{className:"text-xs text-muted-foreground",children:[r.carbonFootprint,"kg CO₂"]})]}),(0,t.jsx)("div",{className:"flex gap-2 mb-3",children:r.colors.map((e,s)=>(0,t.jsx)("button",{type:"button",className:(0,d.cn)("w-6 h-6 rounded-full border-2 transition-all duration-200",b===e.image?"border-primary scale-110":"border-gray-300 hover:border-gray-400"),style:{backgroundColor:e.value},onMouseEnter:()=>N(e.image),onMouseLeave:w,title:e.name},s))}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[r.sizes.slice(0,4).map((e,s)=>(0,t.jsx)(u.E,{variant:"outline",className:"text-xs px-2 py-1",children:e},s)),r.sizes.length>4&&(0,t.jsxs)(u.E,{variant:"outline",className:"text-xs px-2 py-1",children:["+",r.sizes.length-4]})]})]})]})}var f=r(4416);let h=(0,i.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var b=r(5797);function v(e){let s,{product:r,isOpen:i,onClose:c}=e,[m,x]=(0,a.useState)(""),[p,v]=(0,a.useState)(""),[y,j]=(0,a.useState)(""),[N,w]=(0,a.useState)(1),[k,C]=(0,a.useState)(!1),{addItem:z}=(0,b.x)();if(!i||!r)return null;""===y&&r.images.length>0&&j(r.images[0]);let S=(e,s)=>{x(e),j(s),C(!1)},A=e=>{v(e),C(!1)};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"fixed inset-0 bg-black/50 z-50",onClick:c}),(0,t.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold",children:"Select Options"}),(0,t.jsx)(g.$,{variant:"ghost",size:"icon",onClick:c,children:(0,t.jsx)(f.A,{className:"w-5 h-5"})})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"relative aspect-square",children:[(0,t.jsx)(l.default,{src:y,alt:r.name,fill:!0,className:"object-cover rounded-lg"}),r.ecoRating&&(0,t.jsxs)(u.E,{className:(0,d.cn)("absolute top-2 left-2 text-white",(0,d.sj)(r.ecoRating)),children:[(0,t.jsx)(o,{className:"w-3 h-3 mr-1"}),r.ecoRating]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[r.brand&&(0,t.jsx)("p",{className:"text-sm text-muted-foreground uppercase tracking-wide",children:r.brand}),(0,t.jsx)("h3",{className:"text-2xl font-bold",children:r.name}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"flex",children:(s=r.rating,Array.from({length:5},(e,r)=>(0,t.jsx)(n,{className:(0,d.cn)("w-4 h-4",r<Math.floor(s)?"fill-yellow-400 text-yellow-400":"text-gray-300")},r)))}),(0,t.jsxs)("span",{className:"text-sm text-muted-foreground",children:["(",r.reviews," reviews)"]})]}),(0,t.jsx)("div",{className:"text-3xl font-bold",children:(0,d.$g)(r.price)}),k&&(!m||!p)&&(0,t.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg",children:[(0,t.jsx)(h,{className:"w-4 h-4 text-red-500"}),(0,t.jsx)("span",{className:"text-sm text-red-700",children:"Please select color and size to add product into cart."})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium",children:["Color ",!m&&k&&(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("div",{className:"flex gap-2 flex-wrap",children:r.colors.map((e,s)=>(0,t.jsxs)("button",{type:"button",className:(0,d.cn)("flex items-center gap-2 px-3 py-2 border rounded-lg transition-all",m===e.name?"border-primary bg-primary/10":"border-gray-300 hover:border-gray-400"),onClick:()=>S(e.name,e.image),children:[(0,t.jsx)("div",{className:"w-4 h-4 rounded-full border",style:{backgroundColor:e.value}}),(0,t.jsx)("span",{className:"text-sm",children:e.name})]},s))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium",children:["Size ",!p&&k&&(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("div",{className:"flex gap-2 flex-wrap",children:r.sizes.map((e,s)=>(0,t.jsx)("button",{type:"button",className:(0,d.cn)("px-3 py-2 border rounded-lg transition-all text-sm",p===e?"border-primary bg-primary/10":"border-gray-300 hover:border-gray-400"),onClick:()=>A(e),children:e},s))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"Quantity"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(g.$,{variant:"outline",size:"icon",onClick:()=>w(Math.max(1,N-1)),children:"-"}),(0,t.jsx)("span",{className:"w-12 text-center",children:N}),(0,t.jsx)(g.$,{variant:"outline",size:"icon",onClick:()=>w(N+1),children:"+"})]})]}),(0,t.jsxs)(g.$,{className:"w-full",size:"lg",onClick:()=>{if(!m||!p)return void C(!0);z(r,m,p,N),x(""),v(""),w(1),C(!1),c()},children:["Add to Cart - ",(0,d.$g)(r.price*N)]}),(0,t.jsxs)("div",{className:"pt-4 border-t",children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"Description"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:r.description})]})]})]})})]})})]})}function y(e){let{products:s}=e,[r,l]=(0,a.useState)(null),[i,n]=(0,a.useState)(!1),c=e=>{l(e),n(!0)},o=e=>{l(e),n(!0)};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:s.map(e=>(0,t.jsx)(p,{product:e,onQuickAdd:c,onViewDetails:o},e.id))}),(0,t.jsx)(v,{product:r,isOpen:i,onClose:()=>{n(!1),l(null)}})]})}},5797:(e,s,r)=>{"use strict";r.d(s,{x:()=>l});var t=r(5453),a=r(6786);let l=(0,t.v)()((0,a.Zr)((e,s)=>({items:[],isOpen:!1,addItem:function(r,t,a){let l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,i=s().items,n=i.findIndex(e=>e.product.id===r.id&&e.selectedColor===t&&e.selectedSize===a);if(n>-1){let s=[...i];s[n].quantity+=l,e({items:s})}else e({items:[...i,{product:r,selectedColor:t,selectedSize:a,quantity:l}]})},removeItem:(r,t,a)=>{e({items:s().items.filter(e=>e.product.id!==r||e.selectedColor!==t||e.selectedSize!==a)})},updateQuantity:(r,t,a,l)=>{if(l<=0)return void s().removeItem(r,t,a);e({items:s().items.map(e=>e.product.id===r&&e.selectedColor===t&&e.selectedSize===a?{...e,quantity:l}:e)})},clearCart:()=>e({items:[]}),getTotalItems:()=>s().items.reduce((e,s)=>e+s.quantity,0),getTotalPrice:()=>s().items.reduce((e,s)=>e+s.product.price*s.quantity,0),setIsOpen:s=>e({isOpen:s}),mergeGuestCart:r=>{let t=[...s().items];r.forEach(e=>{let s=t.findIndex(s=>s.product.id===e.product.id&&s.selectedColor===e.selectedColor&&s.selectedSize===e.selectedSize);s>-1?t[s].quantity+=e.quantity:t.push(e)}),e({items:t})}}),{name:"cart-storage",storage:(0,a.KU)(()=>localStorage)}))},6126:(e,s,r)=>{"use strict";r.d(s,{E:()=>l});var t=r(5155);r(2115);var a=r(9434);function l(e){let{className:s,variant:r="default",...l}=e;return(0,t.jsx)("div",{className:(0,a.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80"}[r],s),...l})}},7594:(e,s,r)=>{Promise.resolve().then(r.bind(r,5226))},9434:(e,s,r)=>{"use strict";r.d(s,{$g:()=>l,cn:()=>a,sj:()=>i});var t=r(2596);function a(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,t.$)(s)}function l(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e)}function i(e){return({A:"bg-green-500",B:"bg-lime-500",C:"bg-yellow-500",D:"bg-orange-500",E:"bg-red-500"})[e]}}},e=>{var s=s=>e(e.s=s);e.O(0,[846,441,684,358],()=>s(7594)),_N_E=e.O()}]);