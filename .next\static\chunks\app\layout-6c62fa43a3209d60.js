(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{285:(e,t,s)=>{"use strict";s.d(t,{$:()=>n});var r=s(5155),a=s(2115),i=s(9434);let n=a.forwardRef((e,t)=>{let{className:s,variant:a="default",size:n="default",...l}=e;return(0,r.jsx)("button",{className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"}[a],{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[n],s),ref:t,...l})});n.displayName="Button"},347:()=>{},2093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},5797:(e,t,s)=>{"use strict";s.d(t,{x:()=>i});var r=s(5453),a=s(6786);let i=(0,r.v)()((0,a.Zr)((e,t)=>({items:[],isOpen:!1,addItem:function(s,r,a){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,n=t().items,l=n.findIndex(e=>e.product.id===s.id&&e.selectedColor===r&&e.selectedSize===a);if(l>-1){let t=[...n];t[l].quantity+=i,e({items:t})}else e({items:[...n,{product:s,selectedColor:r,selectedSize:a,quantity:i}]})},removeItem:(s,r,a)=>{e({items:t().items.filter(e=>e.product.id!==s||e.selectedColor!==r||e.selectedSize!==a)})},updateQuantity:(s,r,a,i)=>{if(i<=0)return void t().removeItem(s,r,a);e({items:t().items.map(e=>e.product.id===s&&e.selectedColor===r&&e.selectedSize===a?{...e,quantity:i}:e)})},clearCart:()=>e({items:[]}),getTotalItems:()=>t().items.reduce((e,t)=>e+t.quantity,0),getTotalPrice:()=>t().items.reduce((e,t)=>e+t.product.price*t.quantity,0),setIsOpen:t=>e({isOpen:t}),mergeGuestCart:s=>{let r=[...t().items];s.forEach(e=>{let t=r.findIndex(t=>t.product.id===e.product.id&&t.selectedColor===e.selectedColor&&t.selectedSize===e.selectedSize);t>-1?r[t].quantity+=e.quantity:r.push(e)}),e({items:r})}}),{name:"cart-storage",storage:(0,a.KU)(()=>localStorage)}))},6126:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var r=s(5155);s(2115);var a=s(9434);function i(e){let{className:t,variant:s="default",...i}=e;return(0,r.jsx)("div",{className:(0,a.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80"}[s],t),...i})}},6478:(e,t,s)=>{"use strict";s.d(t,{CartSidebar:()=>x});var r=s(5155),a=s(4416),i=s(9946);let n=(0,i.A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]),l=(0,i.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),c=(0,i.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var o=s(6766),d=s(285),m=s(6126),u=s(5797),h=s(9434);function x(){let{items:e,isOpen:t,setIsOpen:s,updateQuantity:i,removeItem:x,getTotalPrice:f,clearCart:p}=(0,u.x)();return t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 z-40",onClick:()=>s(!1)}),(0,r.jsxs)("div",{className:"fixed right-0 top-0 h-full w-full max-w-md bg-white z-50 shadow-xl flex flex-col",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"Shopping Cart"}),(0,r.jsx)(d.$,{variant:"ghost",size:"icon",onClick:()=>s(!1),children:(0,r.jsx)(a.A,{className:"w-5 h-5"})})]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:0===e.length?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full text-center",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4",children:(0,r.jsx)("svg",{className:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})})}),(0,r.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Your cart is empty"}),(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"Add some items to get started"}),(0,r.jsx)(d.$,{onClick:()=>s(!1),children:"Continue Shopping"})]}):(0,r.jsx)("div",{className:"space-y-4",children:e.map((e,t)=>{var s;return(0,r.jsxs)("div",{className:"flex gap-3 p-3 border rounded-lg",children:[(0,r.jsx)("div",{className:"relative w-16 h-16 flex-shrink-0",children:(0,r.jsx)(o.default,{src:e.product.images[0],alt:e.product.name,fill:!0,className:"object-cover rounded"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h4",{className:"font-medium text-sm truncate",children:e.product.name}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full border",style:{backgroundColor:null==(s=e.product.colors.find(t=>t.name===e.selectedColor))?void 0:s.value}}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:e.selectedColor}),(0,r.jsx)(m.E,{variant:"outline",className:"text-xs",children:e.selectedSize})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,r.jsx)("span",{className:"font-semibold text-sm",children:(0,h.$g)(e.product.price)}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(d.$,{variant:"outline",size:"icon",className:"w-6 h-6",onClick:()=>i(e.product.id,e.selectedColor,e.selectedSize,e.quantity-1),children:(0,r.jsx)(n,{className:"w-3 h-3"})}),(0,r.jsx)("span",{className:"w-8 text-center text-sm",children:e.quantity}),(0,r.jsx)(d.$,{variant:"outline",size:"icon",className:"w-6 h-6",onClick:()=>i(e.product.id,e.selectedColor,e.selectedSize,e.quantity+1),children:(0,r.jsx)(l,{className:"w-3 h-3"})}),(0,r.jsx)(d.$,{variant:"ghost",size:"icon",className:"w-6 h-6 text-red-500 hover:text-red-700",onClick:()=>x(e.product.id,e.selectedColor,e.selectedSize),children:(0,r.jsx)(c,{className:"w-3 h-3"})})]})]})]})]},"".concat(e.product.id,"-").concat(e.selectedColor,"-").concat(e.selectedSize))})})}),e.length>0&&(0,r.jsxs)("div",{className:"border-t p-4 space-y-4",children:[(0,r.jsx)(d.$,{variant:"outline",className:"w-full",onClick:p,children:"Clear Cart"}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-lg font-semibold",children:[(0,r.jsx)("span",{children:"Total:"}),(0,r.jsx)("span",{children:(0,h.$g)(f())})]}),(0,r.jsx)(d.$,{className:"w-full",size:"lg",children:"Checkout"})]})]})]}):null}},7735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},9434:(e,t,s)=>{"use strict";s.d(t,{$g:()=>i,cn:()=>a,sj:()=>n});var r=s(2596);function a(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.$)(t)}function i(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e)}function n(e){return({A:"bg-green-500",B:"bg-lime-500",C:"bg-yellow-500",D:"bg-orange-500",E:"bg-red-500"})[e]}},9584:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2093,23)),Promise.resolve().then(s.t.bind(s,7735,23)),Promise.resolve().then(s.t.bind(s,347,23)),Promise.resolve().then(s.bind(s,6478)),Promise.resolve().then(s.bind(s,9780))},9780:(e,t,s)=>{"use strict";s.d(t,{Header:()=>f});var r=s(5155),a=s(9946);let i=(0,a.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),n=(0,a.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var l=s(1976);let c=(0,a.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);var o=s(285);let d=(0,a.A)("shopping-bag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]);var m=s(6126),u=s(5797),h=s(9434);function x(e){let{className:t}=e,{getTotalItems:s,setIsOpen:a}=(0,u.x)(),i=s();return(0,r.jsxs)(o.$,{variant:"ghost",size:"icon",className:(0,h.cn)("relative",t),onClick:()=>a(!0),children:[(0,r.jsx)(d,{className:"w-6 h-6"}),i>0&&(0,r.jsx)(m.E,{className:"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs bg-primary text-primary-foreground",children:i>99?"99+":i})]})}function f(){return(0,r.jsx)("header",{className:"sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,r.jsx)(o.$,{variant:"ghost",size:"icon",className:"md:hidden",children:(0,r.jsx)(i,{className:"w-5 h-5"})}),(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)("h1",{className:"text-xl font-bold",children:"NextGen Store"})}),(0,r.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[(0,r.jsx)("a",{href:"#",className:"text-sm font-medium hover:text-primary transition-colors",children:"New Arrivals"}),(0,r.jsx)("a",{href:"#",className:"text-sm font-medium hover:text-primary transition-colors",children:"Clothing"}),(0,r.jsx)("a",{href:"#",className:"text-sm font-medium hover:text-primary transition-colors",children:"Electronics"}),(0,r.jsx)("a",{href:"#",className:"text-sm font-medium hover:text-primary transition-colors",children:"Accessories"}),(0,r.jsx)("a",{href:"#",className:"text-sm font-medium hover:text-primary transition-colors",children:"Sustainable"})]}),(0,r.jsx)("div",{className:"hidden md:flex items-center flex-1 max-w-md mx-6",children:(0,r.jsxs)("div",{className:"relative w-full",children:[(0,r.jsx)(n,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4"}),(0,r.jsx)("input",{type:"text",placeholder:"Search products...",className:"w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(o.$,{variant:"ghost",size:"icon",className:"md:hidden",children:(0,r.jsx)(n,{className:"w-5 h-5"})}),(0,r.jsx)(o.$,{variant:"ghost",size:"icon",children:(0,r.jsx)(l.A,{className:"w-5 h-5"})}),(0,r.jsx)(o.$,{variant:"ghost",size:"icon",children:(0,r.jsx)(c,{className:"w-5 h-5"})}),(0,r.jsx)(x,{})]})]}),(0,r.jsx)("div",{className:"md:hidden pb-4",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(n,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4"}),(0,r.jsx)("input",{type:"text",placeholder:"Search products...",className:"w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"})]})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[360,846,441,684,358],()=>t(9584)),_N_E=e.O()}]);