{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/product/product-card.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Image from 'next/image';\nimport { Star, Heart, Leaf } from 'lucide-react';\nimport { Product } from '@/types';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { formatPrice, getEcoRatingColor } from '@/lib/utils';\nimport { cn } from '@/lib/utils';\n\ninterface ProductCardProps {\n  product: Product;\n  onQuickAdd?: (product: Product) => void;\n  onViewDetails?: (product: Product) => void;\n}\n\nexport function ProductCard({ product, onQuickAdd, onViewDetails }: ProductCardProps) {\n  const [currentImage, setCurrentImage] = useState(product.images[0]);\n  const [hoveredColor, setHoveredColor] = useState<string | null>(null);\n  const [isLiked, setIsLiked] = useState(false);\n\n  const handleColorHover = (colorImage: string) => {\n    setCurrentImage(colorImage);\n    setHoveredColor(colorImage);\n  };\n\n  const handleColorLeave = () => {\n    if (!hoveredColor) return;\n    setCurrentImage(product.images[0]);\n    setHoveredColor(null);\n  };\n\n  const renderStars = (rating: number) => {\n    return Array.from({ length: 5 }, (_, i) => (\n      <Star\n        key={i}\n        className={cn(\n          \"w-4 h-4\",\n          i < Math.floor(rating)\n            ? \"fill-yellow-400 text-yellow-400\"\n            : \"text-gray-300\"\n        )}\n      />\n    ));\n  };\n\n  return (\n    <Card className=\"group relative overflow-hidden transition-all duration-300 hover:shadow-lg\">\n      {/* Wishlist Button */}\n      <Button\n        variant=\"ghost\"\n        size=\"icon\"\n        className=\"absolute top-2 right-2 z-10 bg-white/80 backdrop-blur-sm hover:bg-white\"\n        onClick={() => setIsLiked(!isLiked)}\n      >\n        <Heart\n          className={cn(\n            \"w-4 h-4 transition-colors\",\n            isLiked ? \"fill-red-500 text-red-500\" : \"text-gray-600\"\n          )}\n        />\n      </Button>\n\n      {/* Eco Rating Badge */}\n      {product.ecoRating && (\n        <Badge\n          className={cn(\n            \"absolute top-2 left-2 z-10 text-white\",\n            getEcoRatingColor(product.ecoRating)\n          )}\n        >\n          <Leaf className=\"w-3 h-3 mr-1\" />\n          {product.ecoRating}\n        </Badge>\n      )}\n\n      {/* Product Image */}\n      <div className=\"relative aspect-square overflow-hidden\">\n        <Image\n          src={currentImage}\n          alt={product.name}\n          fill\n          className=\"object-cover transition-transform duration-300 group-hover:scale-105\"\n          sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n        />\n\n        {/* Quick Add Button - appears on hover */}\n        <div className=\"absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center p-4\">\n          <Button\n            onClick={() => onQuickAdd?.(product)}\n            className=\"w-full bg-white text-black hover:bg-gray-100\"\n          >\n            Quick Add\n          </Button>\n        </div>\n      </div>\n\n      <CardContent className=\"p-4\">\n        {/* Brand */}\n        {product.brand && (\n          <p className=\"text-xs text-muted-foreground uppercase tracking-wide mb-1\">\n            {product.brand}\n          </p>\n        )}\n\n        {/* Product Name */}\n        <h3\n          className=\"font-semibold text-sm mb-2 line-clamp-2 cursor-pointer hover:text-primary transition-colors\"\n          onClick={() => onViewDetails?.(product)}\n        >\n          {product.name}\n        </h3>\n\n        {/* Rating */}\n        <div className=\"flex items-center gap-1 mb-2\">\n          <div className=\"flex\">\n            {renderStars(product.rating)}\n          </div>\n          <span className=\"text-xs text-muted-foreground ml-1\">\n            ({product.reviews})\n          </span>\n        </div>\n\n        {/* Price */}\n        <div className=\"flex items-center justify-between mb-3\">\n          <span className=\"text-lg font-bold\">\n            {formatPrice(product.price)}\n          </span>\n          {product.carbonFootprint && (\n            <span className=\"text-xs text-muted-foreground\">\n              {product.carbonFootprint}kg CO₂\n            </span>\n          )}\n        </div>\n\n        {/* Color Swatches */}\n        <div className=\"flex gap-2 mb-3\">\n          {product.colors.map((color, index) => (\n            <button\n              key={index}\n              type=\"button\"\n              className={cn(\n                \"w-6 h-6 rounded-full border-2 transition-all duration-200\",\n                hoveredColor === color.image\n                  ? \"border-primary scale-110\"\n                  : \"border-gray-300 hover:border-gray-400\"\n              )}\n              style={{ backgroundColor: color.value }}\n              onMouseEnter={() => handleColorHover(color.image)}\n              onMouseLeave={handleColorLeave}\n              title={color.name}\n            />\n          ))}\n        </div>\n\n        {/* Available Sizes */}\n        <div className=\"flex flex-wrap gap-1\">\n          {product.sizes.slice(0, 4).map((size, index) => (\n            <Badge key={index} variant=\"outline\" className=\"text-xs px-2 py-1\">\n              {size}\n            </Badge>\n          ))}\n          {product.sizes.length > 4 && (\n            <Badge variant=\"outline\" className=\"text-xs px-2 py-1\">\n              +{product.sizes.length - 4}\n            </Badge>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAkBO,SAAS,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAoB;;IAClF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,MAAM,CAAC,EAAE;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,cAAc;QACnB,gBAAgB,QAAQ,MAAM,CAAC,EAAE;QACjC,gBAAgB;IAClB;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,kBACnC,6LAAC,qMAAA,CAAA,OAAI;gBAEH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,WACA,IAAI,KAAK,KAAK,CAAC,UACX,oCACA;eALD;;;;;IASX;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BAEd,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,WAAW,CAAC;0BAE3B,cAAA,6LAAC,uMAAA,CAAA,QAAK;oBACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6BACA,UAAU,8BAA8B;;;;;;;;;;;YAM7C,QAAQ,SAAS,kBAChB,6LAAC,oIAAA,CAAA,QAAK;gBACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yCACA,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,SAAS;;kCAGrC,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACf,QAAQ,SAAS;;;;;;;0BAKtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK;wBACL,KAAK,QAAQ,IAAI;wBACjB,IAAI;wBACJ,WAAU;wBACV,OAAM;;;;;;kCAIR,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,aAAa;4BAC5B,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAML,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;oBAEpB,QAAQ,KAAK,kBACZ,6LAAC;wBAAE,WAAU;kCACV,QAAQ,KAAK;;;;;;kCAKlB,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,gBAAgB;kCAE9B,QAAQ,IAAI;;;;;;kCAIf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,YAAY,QAAQ,MAAM;;;;;;0CAE7B,6LAAC;gCAAK,WAAU;;oCAAqC;oCACjD,QAAQ,OAAO;oCAAC;;;;;;;;;;;;;kCAKtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;4BAE3B,QAAQ,eAAe,kBACtB,6LAAC;gCAAK,WAAU;;oCACb,QAAQ,eAAe;oCAAC;;;;;;;;;;;;;kCAM/B,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC;gCAEC,MAAK;gCACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6DACA,iBAAiB,MAAM,KAAK,GACxB,6BACA;gCAEN,OAAO;oCAAE,iBAAiB,MAAM,KAAK;gCAAC;gCACtC,cAAc,IAAM,iBAAiB,MAAM,KAAK;gCAChD,cAAc;gCACd,OAAO,MAAM,IAAI;+BAXZ;;;;;;;;;;kCAiBX,6LAAC;wBAAI,WAAU;;4BACZ,QAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACpC,6LAAC,oIAAA,CAAA,QAAK;oCAAa,SAAQ;oCAAU,WAAU;8CAC5C;mCADS;;;;;4BAIb,QAAQ,KAAK,CAAC,MAAM,GAAG,mBACtB,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;oCAAoB;oCACnD,QAAQ,KAAK,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;AAOvC;GA3JgB;KAAA", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/product/product-selection-modal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { X, Star, Leaf, AlertCircle } from 'lucide-react';\nimport Image from 'next/image';\nimport { Product } from '@/types';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { useCartStore } from '@/store/cart';\nimport { formatPrice, getEcoRatingColor, cn } from '@/lib/utils';\n\ninterface ProductSelectionModalProps {\n  product: Product | null;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function ProductSelectionModal({ product, isOpen, onClose }: ProductSelectionModalProps) {\n  const [selectedColor, setSelectedColor] = useState<string>('');\n  const [selectedSize, setSelectedSize] = useState<string>('');\n  const [currentImage, setCurrentImage] = useState<string>('');\n  const [quantity, setQuantity] = useState(1);\n  const [showValidation, setShowValidation] = useState(false);\n\n  const { addItem } = useCartStore();\n\n  if (!isOpen || !product) return null;\n\n  // Reset state when product changes\n  if (currentImage === '' && product.images.length > 0) {\n    setCurrentImage(product.images[0]);\n  }\n\n  const handleColorSelect = (colorName: string, colorImage: string) => {\n    setSelectedColor(colorName);\n    setCurrentImage(colorImage);\n    setShowValidation(false);\n  };\n\n  const handleSizeSelect = (size: string) => {\n    setSelectedSize(size);\n    setShowValidation(false);\n  };\n\n  const handleAddToCart = () => {\n    if (!selectedColor || !selectedSize) {\n      setShowValidation(true);\n      return;\n    }\n\n    addItem(product, selectedColor, selectedSize, quantity);\n\n    // Reset form\n    setSelectedColor('');\n    setSelectedSize('');\n    setQuantity(1);\n    setShowValidation(false);\n    onClose();\n  };\n\n  const renderStars = (rating: number) => {\n    return Array.from({ length: 5 }, (_, i) => (\n      <Star\n        key={i}\n        className={cn(\n          \"w-4 h-4\",\n          i < Math.floor(rating)\n            ? \"fill-yellow-400 text-yellow-400\"\n            : \"text-gray-300\"\n        )}\n      />\n    ));\n  };\n\n  return (\n    <>\n      {/* Backdrop */}\n      <div\n        className=\"fixed inset-0 bg-black/50 z-50\"\n        onClick={onClose}\n      />\n\n      {/* Modal */}\n      <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\n        <div className=\"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-6 border-b\">\n            <h2 className=\"text-xl font-semibold\">Select Options</h2>\n            <Button variant=\"ghost\" size=\"icon\" onClick={onClose}>\n              <X className=\"w-5 h-5\" />\n            </Button>\n          </div>\n\n          <div className=\"p-6\">\n            <div className=\"grid md:grid-cols-2 gap-6\">\n              {/* Product Image */}\n              <div className=\"relative aspect-square\">\n                <Image\n                  src={currentImage}\n                  alt={product.name}\n                  fill\n                  className=\"object-cover rounded-lg\"\n                />\n                {product.ecoRating && (\n                  <Badge\n                    className={cn(\n                      \"absolute top-2 left-2 text-white\",\n                      getEcoRatingColor(product.ecoRating)\n                    )}\n                  >\n                    <Leaf className=\"w-3 h-3 mr-1\" />\n                    {product.ecoRating}\n                  </Badge>\n                )}\n              </div>\n\n              {/* Product Details */}\n              <div className=\"space-y-4\">\n                {/* Brand & Name */}\n                {product.brand && (\n                  <p className=\"text-sm text-muted-foreground uppercase tracking-wide\">\n                    {product.brand}\n                  </p>\n                )}\n                <h3 className=\"text-2xl font-bold\">{product.name}</h3>\n\n                {/* Rating */}\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"flex\">\n                    {renderStars(product.rating)}\n                  </div>\n                  <span className=\"text-sm text-muted-foreground\">\n                    ({product.reviews} reviews)\n                  </span>\n                </div>\n\n                {/* Price */}\n                <div className=\"text-3xl font-bold\">\n                  {formatPrice(product.price)}\n                </div>\n\n                {/* Validation Message */}\n                {showValidation && (!selectedColor || !selectedSize) && (\n                  <div className=\"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg\">\n                    <AlertCircle className=\"w-4 h-4 text-red-500\" />\n                    <span className=\"text-sm text-red-700\">\n                      Please select color and size to add product into cart.\n                    </span>\n                  </div>\n                )}\n\n                {/* Color Selection */}\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">\n                    Color {!selectedColor && showValidation && <span className=\"text-red-500\">*</span>}\n                  </label>\n                  <div className=\"flex gap-2 flex-wrap\">\n                    {product.colors.map((color, index) => (\n                      <button\n                        key={index}\n                        type=\"button\"\n                        className={cn(\n                          \"flex items-center gap-2 px-3 py-2 border rounded-lg transition-all\",\n                          selectedColor === color.name\n                            ? \"border-primary bg-primary/10\"\n                            : \"border-gray-300 hover:border-gray-400\"\n                        )}\n                        onClick={() => handleColorSelect(color.name, color.image)}\n                      >\n                        <div\n                          className=\"w-4 h-4 rounded-full border\"\n                          style={{ backgroundColor: color.value }}\n                        />\n                        <span className=\"text-sm\">{color.name}</span>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Size Selection */}\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">\n                    Size {!selectedSize && showValidation && <span className=\"text-red-500\">*</span>}\n                  </label>\n                  <div className=\"flex gap-2 flex-wrap\">\n                    {product.sizes.map((size, index) => (\n                      <button\n                        key={index}\n                        type=\"button\"\n                        className={cn(\n                          \"px-3 py-2 border rounded-lg transition-all text-sm\",\n                          selectedSize === size\n                            ? \"border-primary bg-primary/10\"\n                            : \"border-gray-300 hover:border-gray-400\"\n                        )}\n                        onClick={() => handleSizeSelect(size)}\n                      >\n                        {size}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Quantity */}\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">Quantity</label>\n                  <div className=\"flex items-center gap-2\">\n                    <Button\n                      variant=\"outline\"\n                      size=\"icon\"\n                      onClick={() => setQuantity(Math.max(1, quantity - 1))}\n                    >\n                      -\n                    </Button>\n                    <span className=\"w-12 text-center\">{quantity}</span>\n                    <Button\n                      variant=\"outline\"\n                      size=\"icon\"\n                      onClick={() => setQuantity(quantity + 1)}\n                    >\n                      +\n                    </Button>\n                  </div>\n                </div>\n\n                {/* Add to Cart Button */}\n                <Button\n                  className=\"w-full\"\n                  size=\"lg\"\n                  onClick={handleAddToCart}\n                >\n                  Add to Cart - {formatPrice(product.price * quantity)}\n                </Button>\n\n                {/* Product Description */}\n                <div className=\"pt-4 border-t\">\n                  <h4 className=\"font-medium mb-2\">Description</h4>\n                  <p className=\"text-sm text-muted-foreground\">\n                    {product.description}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;;;AATA;;;;;;;;AAiBO,SAAS,sBAAsB,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAA8B;;IAC5F,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;IAE/B,IAAI,CAAC,UAAU,CAAC,SAAS,OAAO;IAEhC,mCAAmC;IACnC,IAAI,iBAAiB,MAAM,QAAQ,MAAM,CAAC,MAAM,GAAG,GAAG;QACpD,gBAAgB,QAAQ,MAAM,CAAC,EAAE;IACnC;IAEA,MAAM,oBAAoB,CAAC,WAAmB;QAC5C,iBAAiB;QACjB,gBAAgB;QAChB,kBAAkB;IACpB;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,kBAAkB;IACpB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,iBAAiB,CAAC,cAAc;YACnC,kBAAkB;YAClB;QACF;QAEA,QAAQ,SAAS,eAAe,cAAc;QAE9C,aAAa;QACb,iBAAiB;QACjB,gBAAgB;QAChB,YAAY;QACZ,kBAAkB;QAClB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,kBACnC,6LAAC,qMAAA,CAAA,OAAI;gBAEH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,WACA,IAAI,KAAK,KAAK,CAAC,UACX,oCACA;eALD;;;;;IASX;IAEA,qBACE;;0BAEE,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,SAAS;8CAC3C,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK;gDACL,KAAK,QAAQ,IAAI;gDACjB,IAAI;gDACJ,WAAU;;;;;;4CAEX,QAAQ,SAAS,kBAChB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oCACA,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,SAAS;;kEAGrC,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,QAAQ,SAAS;;;;;;;;;;;;;kDAMxB,6LAAC;wCAAI,WAAU;;4CAEZ,QAAQ,KAAK,kBACZ,6LAAC;gDAAE,WAAU;0DACV,QAAQ,KAAK;;;;;;0DAGlB,6LAAC;gDAAG,WAAU;0DAAsB,QAAQ,IAAI;;;;;;0DAGhD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,YAAY,QAAQ,MAAM;;;;;;kEAE7B,6LAAC;wDAAK,WAAU;;4DAAgC;4DAC5C,QAAQ,OAAO;4DAAC;;;;;;;;;;;;;0DAKtB,6LAAC;gDAAI,WAAU;0DACZ,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;4CAI3B,kBAAkB,CAAC,CAAC,iBAAiB,CAAC,YAAY,mBACjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,6LAAC;wDAAK,WAAU;kEAAuB;;;;;;;;;;;;0DAO3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;;4DAAsB;4DAC9B,CAAC,iBAAiB,gCAAkB,6LAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAE5E,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC1B,6LAAC;gEAEC,MAAK;gEACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sEACA,kBAAkB,MAAM,IAAI,GACxB,iCACA;gEAEN,SAAS,IAAM,kBAAkB,MAAM,IAAI,EAAE,MAAM,KAAK;;kFAExD,6LAAC;wEACC,WAAU;wEACV,OAAO;4EAAE,iBAAiB,MAAM,KAAK;wEAAC;;;;;;kFAExC,6LAAC;wEAAK,WAAU;kFAAW,MAAM,IAAI;;;;;;;+DAdhC;;;;;;;;;;;;;;;;0DAqBb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;;4DAAsB;4DAC/B,CAAC,gBAAgB,gCAAkB,6LAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAE1E,6LAAC;wDAAI,WAAU;kEACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC;gEAEC,MAAK;gEACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,iBAAiB,OACb,iCACA;gEAEN,SAAS,IAAM,iBAAiB;0EAE/B;+DAVI;;;;;;;;;;;;;;;;0DAiBb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,YAAY,KAAK,GAAG,CAAC,GAAG,WAAW;0EACnD;;;;;;0EAGD,6LAAC;gEAAK,WAAU;0EAAoB;;;;;;0EACpC,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,YAAY,WAAW;0EACvC;;;;;;;;;;;;;;;;;;0DAOL,6LAAC,qIAAA,CAAA,SAAM;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS;;oDACV;oDACgB,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK,GAAG;;;;;;;0DAI7C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,6LAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxC;GAvOgB;;QAOM,uHAAA,CAAA,eAAY;;;KAPlB", "debugId": null}}, {"offset": {"line": 872, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/product/product-grid.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Product } from '@/types';\nimport { ProductCard } from './product-card';\nimport { ProductSelectionModal } from './product-selection-modal';\n\ninterface ProductGridProps {\n  products: Product[];\n}\n\nexport function ProductGrid({ products }: ProductGridProps) {\n  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n\n  const handleQuickAdd = (product: Product) => {\n    setSelectedProduct(product);\n    setIsModalOpen(true);\n  };\n\n  const handleViewDetails = (product: Product) => {\n    setSelectedProduct(product);\n    setIsModalOpen(true);\n  };\n\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedProduct(null);\n  };\n\n  return (\n    <>\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {products.map((product) => (\n          <ProductCard\n            key={product.id}\n            product={product}\n            onQuickAdd={handleQuickAdd}\n            onViewDetails={handleViewDetails}\n          />\n        ))}\n      </div>\n\n      <ProductSelectionModal\n        product={selectedProduct}\n        isOpen={isModalOpen}\n        onClose={handleCloseModal}\n      />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;;;AALA;;;;AAWO,SAAS,YAAY,EAAE,QAAQ,EAAoB;;IACxD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,iBAAiB,CAAC;QACtB,mBAAmB;QACnB,eAAe;IACjB;IAEA,MAAM,oBAAoB,CAAC;QACzB,mBAAmB;QACnB,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,mBAAmB;IACrB;IAEA,qBACE;;0BACE,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,mJAAA,CAAA,cAAW;wBAEV,SAAS;wBACT,YAAY;wBACZ,eAAe;uBAHV,QAAQ,EAAE;;;;;;;;;;0BAQrB,6LAAC,iKAAA,CAAA,wBAAqB;gBACpB,SAAS;gBACT,QAAQ;gBACR,SAAS;;;;;;;;AAIjB;GAvCgB;KAAA", "debugId": null}}, {"offset": {"line": 944, "column": 0}, "map": {"version": 3, "file": "star.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/node_modules/lucide-react/src/icons/star.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z',\n      key: 'r04s7s',\n    },\n  ],\n];\n\n/**\n * @component @name Star\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEuNTI1IDIuMjk1YS41My41MyAwIDAgMSAuOTUgMGwyLjMxIDQuNjc5YTIuMTIzIDIuMTIzIDAgMCAwIDEuNTk1IDEuMTZsNS4xNjYuNzU2YS41My41MyAwIDAgMSAuMjk0LjkwNGwtMy43MzYgMy42MzhhMi4xMjMgMi4xMjMgMCAwIDAtLjYxMSAxLjg3OGwuODgyIDUuMTRhLjUzLjUzIDAgMCAxLS43NzEuNTZsLTQuNjE4LTIuNDI4YTIuMTIyIDIuMTIyIDAgMCAwLTEuOTczIDBMNi4zOTYgMjEuMDFhLjUzLjUzIDAgMCAxLS43Ny0uNTZsLjg4MS01LjEzOWEyLjEyMiAyLjEyMiAwIDAgMC0uNjExLTEuODc5TDIuMTYgOS43OTVhLjUzLjUzIDAgMCAxIC4yOTQtLjkwNmw1LjE2NS0uNzU1YTIuMTIyIDIuMTIyIDAgMCAwIDEuNTk3LTEuMTZ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/star\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Star = createLucideIcon('star', __iconNode);\n\nexport default Star;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;CACF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "file": "leaf.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/node_modules/lucide-react/src/icons/leaf.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z',\n      key: 'nnexq3',\n    },\n  ],\n  ['path', { d: 'M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12', key: 'mt58a7' }],\n];\n\n/**\n * @component @name Leaf\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEgMjBBNyA3IDAgMCAxIDkuOCA2LjFDMTUuNSA1IDE3IDQuNDggMTkgMmMxIDIgMiA0LjE4IDIgOCAwIDUuNS00Ljc4IDEwLTEwIDEwWiIgLz4KICA8cGF0aCBkPSJNMiAyMWMwLTMgMS44NS01LjM2IDUuMDgtNkM5LjUgMTQuNTIgMTIgMTMgMTMgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/leaf\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Leaf = createLucideIcon('leaf', __iconNode);\n\nexport default Leaf;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAoD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACnF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1029, "column": 0}, "map": {"version": 3, "file": "circle-alert.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/node_modules/lucide-react/src/icons/circle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n];\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('circle-alert', __iconNode);\n\nexport default CircleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACvE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}