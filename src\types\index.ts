export interface Product {
  id: string;
  name: string;
  price: number;
  images: string[];
  colors: Array<{
    name: string;
    value: string;
    image: string;
  }>;
  sizes: string[];
  description: string;
  category: string;
  inStock: boolean;
  rating: number;
  reviews: number;
  ecoRating?: 'A' | 'B' | 'C' | 'D' | 'E';
  carbonFootprint?: number; // in kg CO2
  tags?: string[];
  brand?: string;
  material?: string;
  careInstructions?: string[];
}

export interface CartItem {
  product: Product;
  selectedColor: string;
  selectedSize: string;
  quantity: number;
}

export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  preferences?: {
    style: string[];
    colors: string[];
    priceRange: [number, number];
    sustainabilityFocus: boolean;
  };
}

export interface Review {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  productId: string;
  rating: number;
  title: string;
  content: string;
  images?: string[];
  verified: boolean;
  helpful: number;
  createdAt: string;
  blockchainVerified?: boolean;
}

export interface AIRecommendation {
  type: 'mood' | 'style' | 'bundle' | 'alternative';
  products: Product[];
  reason: string;
  confidence: number;
}

export interface FlashSale {
  id: string;
  productId: string;
  originalPrice: number;
  salePrice: number;
  discount: number;
  expiresAt: string;
  isPersonalized: boolean;
  reason?: string;
}

export interface Theme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    foreground: string;
  };
  fonts: {
    heading: string;
    body: string;
  };
}

export interface FilterOptions {
  category?: string;
  priceRange?: [number, number];
  colors?: string[];
  sizes?: string[];
  brands?: string[];
  rating?: number;
  inStock?: boolean;
  ecoRating?: string[];
  intent?: 'gift' | 'urgent' | 'eco-friendly' | 'budget' | 'luxury';
}

export interface SearchResult {
  products: Product[];
  total: number;
  filters: {
    categories: Array<{ name: string; count: number }>;
    brands: Array<{ name: string; count: number }>;
    priceRange: [number, number];
    colors: Array<{ name: string; value: string; count: number }>;
    sizes: Array<{ name: string; count: number }>;
  };
}
