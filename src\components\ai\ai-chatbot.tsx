'use client';

import { useState, useRef, useEffect } from 'react';
import { MessageCircle, X, Send, Bot, User, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { analyzeUserMood } from '@/lib/utils';

interface Message {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: Date;
  mood?: 'cheerful' | 'indecisive' | 'frustrated' | 'focused';
  recommendations?: Array<{
    productId: string;
    reason: string;
  }>;
}

export function AIChatbot() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'bot',
      content: "Hi! I'm your AI personal shopper. I can sense you're browsing with a focused mindset today. How can I help you find the perfect products?",
      timestamp: new Date(),
      mood: 'focused'
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const getMoodColor = (mood: string) => {
    const colors = {
      cheerful: 'bg-yellow-100 text-yellow-800',
      indecisive: 'bg-blue-100 text-blue-800',
      frustrated: 'bg-red-100 text-red-800',
      focused: 'bg-green-100 text-green-800'
    };
    return colors[mood as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const generateBotResponse = (userMessage: string): string => {
    const lowerMessage = userMessage.toLowerCase();
    
    // Simple AI responses based on keywords
    if (lowerMessage.includes('sustainable') || lowerMessage.includes('eco')) {
      return "Great choice! I love that you're thinking sustainably. Our Eco-Friendly Cotton T-Shirt has an 'A' eco-rating and only 2.1kg CO₂ footprint. It's made from 100% organic cotton and ethically manufactured. Would you like to see more sustainable options?";
    }
    
    if (lowerMessage.includes('budget') || lowerMessage.includes('cheap') || lowerMessage.includes('affordable')) {
      return "I understand you're looking for great value! Our Eco-Friendly Cotton T-Shirt at $29.99 offers excellent quality for the price. It's sustainable, comfortable, and versatile. For accessories, the Minimalist Leather Wallet at $49.99 is also a great investment piece.";
    }
    
    if (lowerMessage.includes('gift') || lowerMessage.includes('present')) {
      return "Perfect! Gift shopping can be tricky, but I'm here to help. What's the occasion and who is it for? For tech lovers, our Wireless Bluetooth Headphones are popular. For fashion-conscious friends, the Premium Denim Jeans are timeless. Tell me more about the recipient!";
    }
    
    if (lowerMessage.includes('electronics') || lowerMessage.includes('headphones') || lowerMessage.includes('tech')) {
      return "Excellent choice! Our Wireless Bluetooth Headphones are premium quality with active noise cancellation and 30-hour battery life. They have a 4.7-star rating and come in Black, White, or Silver. The audio quality is crystal-clear - perfect for music lovers and professionals.";
    }
    
    if (lowerMessage.includes('clothing') || lowerMessage.includes('shirt') || lowerMessage.includes('jeans')) {
      return "I can help you find the perfect clothing! Are you looking for casual wear like our organic cotton t-shirt, or something more structured like our premium denim jeans? What's your style preference and what occasions will you be wearing them for?";
    }
    
    if (lowerMessage.includes('confused') || lowerMessage.includes('help') || lowerMessage.includes('don\'t know')) {
      return "No worries at all! Let me help you narrow things down. What are you shopping for today? Are you looking for: 1) Something for yourself or as a gift? 2) Clothing, electronics, or accessories? 3) Any specific budget range? I'll guide you to the perfect products!";
    }
    
    // Default responses
    const defaultResponses = [
      "That's interesting! Based on your browsing pattern, I think you might like our curated selection. What type of products are you most interested in today?",
      "I'm here to help you find exactly what you need! Can you tell me more about what you're looking for?",
      "Great question! Let me think about the best recommendations for you. What's most important to you - quality, price, sustainability, or style?",
    ];
    
    return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Simulate AI thinking time
    setTimeout(() => {
      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: generateBotResponse(inputValue),
        timestamp: new Date(),
        mood: analyzeUserMood([{ timeSpent: 120, type: 'message' }])
      };

      setMessages(prev => [...prev, botResponse]);
      setIsTyping(false);
    }, 1000 + Math.random() * 2000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 h-14 w-14 rounded-full shadow-lg bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 z-50"
        size="icon"
      >
        <MessageCircle className="w-6 h-6" />
      </Button>
    );
  }

  return (
    <div className="fixed bottom-6 right-6 w-80 h-96 bg-white rounded-lg shadow-xl border z-50 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-t-lg">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
            <Bot className="w-5 h-5" />
          </div>
          <div>
            <h3 className="font-semibold">AI Personal Shopper</h3>
            <p className="text-xs opacity-90">Emotion-aware assistant</p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setIsOpen(false)}
          className="text-white hover:bg-white/20"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] rounded-lg p-3 ${
                message.type === 'user'
                  ? 'bg-purple-500 text-white'
                  : 'bg-gray-100 text-gray-800'
              }`}
            >
              <div className="flex items-center gap-2 mb-1">
                {message.type === 'bot' ? (
                  <Bot className="w-4 h-4" />
                ) : (
                  <User className="w-4 h-4" />
                )}
                {message.mood && (
                  <Badge className={`text-xs ${getMoodColor(message.mood)}`}>
                    <Sparkles className="w-3 h-3 mr-1" />
                    {message.mood}
                  </Badge>
                )}
              </div>
              <p className="text-sm">{message.content}</p>
              <p className="text-xs opacity-70 mt-1">
                {message.timestamp.toLocaleTimeString([], { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </p>
            </div>
          </div>
        ))}
        
        {isTyping && (
          <div className="flex justify-start">
            <div className="bg-gray-100 rounded-lg p-3 max-w-[80%]">
              <div className="flex items-center gap-2">
                <Bot className="w-4 h-4" />
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t">
        <div className="flex gap-2">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask me anything..."
            className="flex-1 px-3 py-2 border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isTyping}
            size="icon"
            className="bg-purple-500 hover:bg-purple-600"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
