{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/product/product-card.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Image from 'next/image';\nimport { Star, Heart, Leaf } from 'lucide-react';\nimport { Product } from '@/types';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { formatPrice, getEcoRatingColor } from '@/lib/utils';\nimport { cn } from '@/lib/utils';\n\ninterface ProductCardProps {\n  product: Product;\n  onQuickAdd?: (product: Product) => void;\n  onViewDetails?: (product: Product) => void;\n}\n\nexport function ProductCard({ product, onQuickAdd, onViewDetails }: ProductCardProps) {\n  const [currentImage, setCurrentImage] = useState(product.images[0]);\n  const [hoveredColor, setHoveredColor] = useState<string | null>(null);\n  const [isLiked, setIsLiked] = useState(false);\n\n  const handleColorHover = (colorImage: string) => {\n    setCurrentImage(colorImage);\n    setHoveredColor(colorImage);\n  };\n\n  const handleColorLeave = () => {\n    if (!hoveredColor) return;\n    setCurrentImage(product.images[0]);\n    setHoveredColor(null);\n  };\n\n  const renderStars = (rating: number) => {\n    return Array.from({ length: 5 }, (_, i) => (\n      <Star\n        key={i}\n        className={cn(\n          \"w-4 h-4\",\n          i < Math.floor(rating)\n            ? \"fill-yellow-400 text-yellow-400\"\n            : \"text-gray-300\"\n        )}\n      />\n    ));\n  };\n\n  return (\n    <Card className=\"group relative overflow-hidden transition-all duration-300 hover:shadow-lg\">\n      {/* Wishlist Button */}\n      <Button\n        variant=\"ghost\"\n        size=\"icon\"\n        className=\"absolute top-2 right-2 z-10 bg-white/80 backdrop-blur-sm hover:bg-white\"\n        onClick={() => setIsLiked(!isLiked)}\n      >\n        <Heart\n          className={cn(\n            \"w-4 h-4 transition-colors\",\n            isLiked ? \"fill-red-500 text-red-500\" : \"text-gray-600\"\n          )}\n        />\n      </Button>\n\n      {/* Eco Rating Badge */}\n      {product.ecoRating && (\n        <Badge\n          className={cn(\n            \"absolute top-2 left-2 z-10 text-white\",\n            getEcoRatingColor(product.ecoRating)\n          )}\n        >\n          <Leaf className=\"w-3 h-3 mr-1\" />\n          {product.ecoRating}\n        </Badge>\n      )}\n\n      {/* Product Image */}\n      <div className=\"relative aspect-square overflow-hidden\">\n        <Image\n          src={currentImage}\n          alt={product.name}\n          fill\n          className=\"object-cover transition-transform duration-300 group-hover:scale-105\"\n          sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n        />\n\n        {/* Quick Add Button - appears on hover */}\n        <div className=\"absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center p-4\">\n          <Button\n            onClick={() => onQuickAdd?.(product)}\n            className=\"w-full bg-white text-black hover:bg-gray-100\"\n          >\n            Quick Add\n          </Button>\n        </div>\n      </div>\n\n      <CardContent className=\"p-4\">\n        {/* Brand */}\n        {product.brand && (\n          <p className=\"text-xs text-muted-foreground uppercase tracking-wide mb-1\">\n            {product.brand}\n          </p>\n        )}\n\n        {/* Product Name */}\n        <h3\n          className=\"font-semibold text-sm mb-2 line-clamp-2 cursor-pointer hover:text-primary transition-colors\"\n          onClick={() => onViewDetails?.(product)}\n        >\n          {product.name}\n        </h3>\n\n        {/* Rating */}\n        <div className=\"flex items-center gap-1 mb-2\">\n          <div className=\"flex\">\n            {renderStars(product.rating)}\n          </div>\n          <span className=\"text-xs text-muted-foreground ml-1\">\n            ({product.reviews})\n          </span>\n        </div>\n\n        {/* Price */}\n        <div className=\"flex items-center justify-between mb-3\">\n          <span className=\"text-lg font-bold\">\n            {formatPrice(product.price)}\n          </span>\n          {product.carbonFootprint && (\n            <span className=\"text-xs text-muted-foreground\">\n              {product.carbonFootprint}kg CO₂\n            </span>\n          )}\n        </div>\n\n        {/* Color Swatches */}\n        <div className=\"flex gap-2 mb-3\">\n          {product.colors.map((color, index) => (\n            <button\n              key={index}\n              type=\"button\"\n              className={cn(\n                \"w-6 h-6 rounded-full border-2 transition-all duration-200\",\n                hoveredColor === color.image\n                  ? \"border-primary scale-110\"\n                  : \"border-gray-300 hover:border-gray-400\"\n              )}\n              style={{ backgroundColor: color.value }}\n              onMouseEnter={() => handleColorHover(color.image)}\n              onMouseLeave={handleColorLeave}\n              title={color.name}\n            />\n          ))}\n        </div>\n\n        {/* Available Sizes */}\n        <div className=\"flex flex-wrap gap-1\">\n          {product.sizes.slice(0, 4).map((size, index) => (\n            <Badge key={index} variant=\"outline\" className=\"text-xs px-2 py-1\">\n              {size}\n            </Badge>\n          ))}\n          {product.sizes.length > 4 && (\n            <Badge variant=\"outline\" className=\"text-xs px-2 py-1\">\n              +{product.sizes.length - 4}\n            </Badge>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AATA;;;;;;;;;;AAkBO,SAAS,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAoB;IAClF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,MAAM,CAAC,EAAE;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,cAAc;QACnB,gBAAgB,QAAQ,MAAM,CAAC,EAAE;QACjC,gBAAgB;IAClB;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,kBACnC,8OAAC,kMAAA,CAAA,OAAI;gBAEH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,WACA,IAAI,KAAK,KAAK,CAAC,UACX,oCACA;eALD;;;;;IASX;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BAEd,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,WAAW,CAAC;0BAE3B,cAAA,8OAAC,oMAAA,CAAA,QAAK;oBACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6BACA,UAAU,8BAA8B;;;;;;;;;;;YAM7C,QAAQ,SAAS,kBAChB,8OAAC,iIAAA,CAAA,QAAK;gBACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yCACA,CAAA,GAAA,mHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,SAAS;;kCAGrC,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACf,QAAQ,SAAS;;;;;;;0BAKtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK;wBACL,KAAK,QAAQ,IAAI;wBACjB,IAAI;wBACJ,WAAU;wBACV,OAAM;;;;;;kCAIR,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,aAAa;4BAC5B,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAML,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;oBAEpB,QAAQ,KAAK,kBACZ,8OAAC;wBAAE,WAAU;kCACV,QAAQ,KAAK;;;;;;kCAKlB,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,gBAAgB;kCAE9B,QAAQ,IAAI;;;;;;kCAIf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,YAAY,QAAQ,MAAM;;;;;;0CAE7B,8OAAC;gCAAK,WAAU;;oCAAqC;oCACjD,QAAQ,OAAO;oCAAC;;;;;;;;;;;;;kCAKtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CACb,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;4BAE3B,QAAQ,eAAe,kBACtB,8OAAC;gCAAK,WAAU;;oCACb,QAAQ,eAAe;oCAAC;;;;;;;;;;;;;kCAM/B,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC;gCAEC,MAAK;gCACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6DACA,iBAAiB,MAAM,KAAK,GACxB,6BACA;gCAEN,OAAO;oCAAE,iBAAiB,MAAM,KAAK;gCAAC;gCACtC,cAAc,IAAM,iBAAiB,MAAM,KAAK;gCAChD,cAAc;gCACd,OAAO,MAAM,IAAI;+BAXZ;;;;;;;;;;kCAiBX,8OAAC;wBAAI,WAAU;;4BACZ,QAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACpC,8OAAC,iIAAA,CAAA,QAAK;oCAAa,SAAQ;oCAAU,WAAU;8CAC5C;mCADS;;;;;4BAIb,QAAQ,KAAK,CAAC,MAAM,GAAG,mBACtB,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;oCAAoB;oCACnD,QAAQ,KAAK,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/product/product-selection-modal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { X, Star, Leaf, AlertCircle } from 'lucide-react';\nimport Image from 'next/image';\nimport { Product } from '@/types';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { useCartStore } from '@/store/cart';\nimport { formatPrice, getEcoRatingColor, cn } from '@/lib/utils';\n\ninterface ProductSelectionModalProps {\n  product: Product | null;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function ProductSelectionModal({ product, isOpen, onClose }: ProductSelectionModalProps) {\n  const [selectedColor, setSelectedColor] = useState<string>('');\n  const [selectedSize, setSelectedSize] = useState<string>('');\n  const [currentImage, setCurrentImage] = useState<string>('');\n  const [quantity, setQuantity] = useState(1);\n  const [showValidation, setShowValidation] = useState(false);\n\n  const { addItem } = useCartStore();\n\n  if (!isOpen || !product) return null;\n\n  // Reset state when product changes\n  if (currentImage === '' && product.images.length > 0) {\n    setCurrentImage(product.images[0]);\n  }\n\n  const handleColorSelect = (colorName: string, colorImage: string) => {\n    setSelectedColor(colorName);\n    setCurrentImage(colorImage);\n    setShowValidation(false);\n  };\n\n  const handleSizeSelect = (size: string) => {\n    setSelectedSize(size);\n    setShowValidation(false);\n  };\n\n  const handleAddToCart = () => {\n    if (!selectedColor || !selectedSize) {\n      setShowValidation(true);\n      return;\n    }\n\n    addItem(product, selectedColor, selectedSize, quantity);\n\n    // Reset form\n    setSelectedColor('');\n    setSelectedSize('');\n    setQuantity(1);\n    setShowValidation(false);\n    onClose();\n  };\n\n  const renderStars = (rating: number) => {\n    return Array.from({ length: 5 }, (_, i) => (\n      <Star\n        key={i}\n        className={cn(\n          \"w-4 h-4\",\n          i < Math.floor(rating)\n            ? \"fill-yellow-400 text-yellow-400\"\n            : \"text-gray-300\"\n        )}\n      />\n    ));\n  };\n\n  return (\n    <>\n      {/* Backdrop */}\n      <div\n        className=\"fixed inset-0 bg-black/50 z-50\"\n        onClick={onClose}\n      />\n\n      {/* Modal */}\n      <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\n        <div className=\"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-6 border-b\">\n            <h2 className=\"text-xl font-semibold\">Select Options</h2>\n            <Button variant=\"ghost\" size=\"icon\" onClick={onClose}>\n              <X className=\"w-5 h-5\" />\n            </Button>\n          </div>\n\n          <div className=\"p-6\">\n            <div className=\"grid md:grid-cols-2 gap-6\">\n              {/* Product Image */}\n              <div className=\"relative aspect-square\">\n                <Image\n                  src={currentImage}\n                  alt={product.name}\n                  fill\n                  className=\"object-cover rounded-lg\"\n                />\n                {product.ecoRating && (\n                  <Badge\n                    className={cn(\n                      \"absolute top-2 left-2 text-white\",\n                      getEcoRatingColor(product.ecoRating)\n                    )}\n                  >\n                    <Leaf className=\"w-3 h-3 mr-1\" />\n                    {product.ecoRating}\n                  </Badge>\n                )}\n              </div>\n\n              {/* Product Details */}\n              <div className=\"space-y-4\">\n                {/* Brand & Name */}\n                {product.brand && (\n                  <p className=\"text-sm text-muted-foreground uppercase tracking-wide\">\n                    {product.brand}\n                  </p>\n                )}\n                <h3 className=\"text-2xl font-bold\">{product.name}</h3>\n\n                {/* Rating */}\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"flex\">\n                    {renderStars(product.rating)}\n                  </div>\n                  <span className=\"text-sm text-muted-foreground\">\n                    ({product.reviews} reviews)\n                  </span>\n                </div>\n\n                {/* Price */}\n                <div className=\"text-3xl font-bold\">\n                  {formatPrice(product.price)}\n                </div>\n\n                {/* Validation Message */}\n                {showValidation && (!selectedColor || !selectedSize) && (\n                  <div className=\"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg\">\n                    <AlertCircle className=\"w-4 h-4 text-red-500\" />\n                    <span className=\"text-sm text-red-700\">\n                      Please select color and size to add product into cart.\n                    </span>\n                  </div>\n                )}\n\n                {/* Color Selection */}\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">\n                    Color {!selectedColor && showValidation && <span className=\"text-red-500\">*</span>}\n                  </label>\n                  <div className=\"flex gap-2 flex-wrap\">\n                    {product.colors.map((color, index) => (\n                      <button\n                        key={index}\n                        type=\"button\"\n                        className={cn(\n                          \"flex items-center gap-2 px-3 py-2 border rounded-lg transition-all\",\n                          selectedColor === color.name\n                            ? \"border-primary bg-primary/10\"\n                            : \"border-gray-300 hover:border-gray-400\"\n                        )}\n                        onClick={() => handleColorSelect(color.name, color.image)}\n                      >\n                        <div\n                          className=\"w-4 h-4 rounded-full border\"\n                          style={{ backgroundColor: color.value }}\n                        />\n                        <span className=\"text-sm\">{color.name}</span>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Size Selection */}\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">\n                    Size {!selectedSize && showValidation && <span className=\"text-red-500\">*</span>}\n                  </label>\n                  <div className=\"flex gap-2 flex-wrap\">\n                    {product.sizes.map((size, index) => (\n                      <button\n                        key={index}\n                        type=\"button\"\n                        className={cn(\n                          \"px-3 py-2 border rounded-lg transition-all text-sm\",\n                          selectedSize === size\n                            ? \"border-primary bg-primary/10\"\n                            : \"border-gray-300 hover:border-gray-400\"\n                        )}\n                        onClick={() => handleSizeSelect(size)}\n                      >\n                        {size}\n                      </button>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Quantity */}\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">Quantity</label>\n                  <div className=\"flex items-center gap-2\">\n                    <Button\n                      variant=\"outline\"\n                      size=\"icon\"\n                      onClick={() => setQuantity(Math.max(1, quantity - 1))}\n                    >\n                      -\n                    </Button>\n                    <span className=\"w-12 text-center\">{quantity}</span>\n                    <Button\n                      variant=\"outline\"\n                      size=\"icon\"\n                      onClick={() => setQuantity(quantity + 1)}\n                    >\n                      +\n                    </Button>\n                  </div>\n                </div>\n\n                {/* Add to Cart Button */}\n                <Button\n                  className=\"w-full\"\n                  size=\"lg\"\n                  onClick={handleAddToCart}\n                >\n                  Add to Cart - {formatPrice(product.price * quantity)}\n                </Button>\n\n                {/* Product Description */}\n                <div className=\"pt-4 border-t\">\n                  <h4 className=\"font-medium mb-2\">Description</h4>\n                  <p className=\"text-sm text-muted-foreground\">\n                    {product.description}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AATA;;;;;;;;;AAiBO,SAAS,sBAAsB,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAA8B;IAC5F,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD;IAE/B,IAAI,CAAC,UAAU,CAAC,SAAS,OAAO;IAEhC,mCAAmC;IACnC,IAAI,iBAAiB,MAAM,QAAQ,MAAM,CAAC,MAAM,GAAG,GAAG;QACpD,gBAAgB,QAAQ,MAAM,CAAC,EAAE;IACnC;IAEA,MAAM,oBAAoB,CAAC,WAAmB;QAC5C,iBAAiB;QACjB,gBAAgB;QAChB,kBAAkB;IACpB;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,kBAAkB;IACpB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,iBAAiB,CAAC,cAAc;YACnC,kBAAkB;YAClB;QACF;QAEA,QAAQ,SAAS,eAAe,cAAc;QAE9C,aAAa;QACb,iBAAiB;QACjB,gBAAgB;QAChB,YAAY;QACZ,kBAAkB;QAClB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,kBACnC,8OAAC,kMAAA,CAAA,OAAI;gBAEH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,WACA,IAAI,KAAK,KAAK,CAAC,UACX,oCACA;eALD;;;;;IASX;IAEA,qBACE;;0BAEE,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,SAAS;8CAC3C,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAIjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK;gDACL,KAAK,QAAQ,IAAI;gDACjB,IAAI;gDACJ,WAAU;;;;;;4CAEX,QAAQ,SAAS,kBAChB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oCACA,CAAA,GAAA,mHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,SAAS;;kEAGrC,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,QAAQ,SAAS;;;;;;;;;;;;;kDAMxB,8OAAC;wCAAI,WAAU;;4CAEZ,QAAQ,KAAK,kBACZ,8OAAC;gDAAE,WAAU;0DACV,QAAQ,KAAK;;;;;;0DAGlB,8OAAC;gDAAG,WAAU;0DAAsB,QAAQ,IAAI;;;;;;0DAGhD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,YAAY,QAAQ,MAAM;;;;;;kEAE7B,8OAAC;wDAAK,WAAU;;4DAAgC;4DAC5C,QAAQ,OAAO;4DAAC;;;;;;;;;;;;;0DAKtB,8OAAC;gDAAI,WAAU;0DACZ,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK;;;;;;4CAI3B,kBAAkB,CAAC,CAAC,iBAAiB,CAAC,YAAY,mBACjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;wDAAK,WAAU;kEAAuB;;;;;;;;;;;;0DAO3C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;;4DAAsB;4DAC9B,CAAC,iBAAiB,gCAAkB,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAE5E,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC;gEAEC,MAAK;gEACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA,kBAAkB,MAAM,IAAI,GACxB,iCACA;gEAEN,SAAS,IAAM,kBAAkB,MAAM,IAAI,EAAE,MAAM,KAAK;;kFAExD,8OAAC;wEACC,WAAU;wEACV,OAAO;4EAAE,iBAAiB,MAAM,KAAK;wEAAC;;;;;;kFAExC,8OAAC;wEAAK,WAAU;kFAAW,MAAM,IAAI;;;;;;;+DAdhC;;;;;;;;;;;;;;;;0DAqBb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;;4DAAsB;4DAC/B,CAAC,gBAAgB,gCAAkB,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAE1E,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACxB,8OAAC;gEAEC,MAAK;gEACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,iBAAiB,OACb,iCACA;gEAEN,SAAS,IAAM,iBAAiB;0EAE/B;+DAVI;;;;;;;;;;;;;;;;0DAiBb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,YAAY,KAAK,GAAG,CAAC,GAAG,WAAW;0EACnD;;;;;;0EAGD,8OAAC;gEAAK,WAAU;0EAAoB;;;;;;0EACpC,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,YAAY,WAAW;0EACvC;;;;;;;;;;;;;;;;;;0DAOL,8OAAC,kIAAA,CAAA,SAAM;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS;;oDACV;oDACgB,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK,GAAG;;;;;;;0DAI7C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,8OAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxC", "debugId": null}}, {"offset": {"line": 828, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/product/product-grid.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Product } from '@/types';\nimport { ProductCard } from './product-card';\nimport { ProductSelectionModal } from './product-selection-modal';\n\ninterface ProductGridProps {\n  products: Product[];\n}\n\nexport function ProductGrid({ products }: ProductGridProps) {\n  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n\n  const handleQuickAdd = (product: Product) => {\n    setSelectedProduct(product);\n    setIsModalOpen(true);\n  };\n\n  const handleViewDetails = (product: Product) => {\n    setSelectedProduct(product);\n    setIsModalOpen(true);\n  };\n\n  const handleCloseModal = () => {\n    setIsModalOpen(false);\n    setSelectedProduct(null);\n  };\n\n  return (\n    <>\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {products.map((product) => (\n          <ProductCard\n            key={product.id}\n            product={product}\n            onQuickAdd={handleQuickAdd}\n            onViewDetails={handleViewDetails}\n          />\n        ))}\n      </div>\n\n      <ProductSelectionModal\n        product={selectedProduct}\n        isOpen={isModalOpen}\n        onClose={handleCloseModal}\n      />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AALA;;;;;AAWO,SAAS,YAAY,EAAE,QAAQ,EAAoB;IACxD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,iBAAiB,CAAC;QACtB,mBAAmB;QACnB,eAAe;IACjB;IAEA,MAAM,oBAAoB,CAAC;QACzB,mBAAmB;QACnB,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,mBAAmB;IACrB;IAEA,qBACE;;0BACE,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,gJAAA,CAAA,cAAW;wBAEV,SAAS;wBACT,YAAY;wBACZ,eAAe;uBAHV,QAAQ,EAAE;;;;;;;;;;0BAQrB,8OAAC,8JAAA,CAAA,wBAAqB;gBACpB,SAAS;gBACT,QAAQ;gBACR,SAAS;;;;;;;;AAIjB", "debugId": null}}, {"offset": {"line": 891, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/features/flash-sale-banner.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Clock, X, Zap } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { formatPrice, getTimeUntilExpiry } from '@/lib/utils';\n\ninterface FlashSale {\n  id: string;\n  productName: string;\n  originalPrice: number;\n  salePrice: number;\n  discount: number;\n  expiresAt: string;\n  reason: string;\n  isPersonalized: boolean;\n}\n\nexport function FlashSaleBanner() {\n  const [isVisible, setIsVisible] = useState(true);\n  const [timeLeft, setTimeLeft] = useState({ hours: 0, minutes: 0, seconds: 0, expired: false });\n\n  // Simulated personalized flash sale\n  const flashSale: FlashSale = {\n    id: '1',\n    productName: 'Eco-Friendly Cotton T-Shirt',\n    originalPrice: 29.99,\n    salePrice: 24.99,\n    discount: 17,\n    expiresAt: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // 10 minutes from now\n    reason: 'You viewed this item 3 times last week',\n    isPersonalized: true\n  };\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      const time = getTimeUntilExpiry(flashSale.expiresAt);\n      setTimeLeft(time);\n      \n      if (time.expired) {\n        setIsVisible(false);\n        clearInterval(timer);\n      }\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [flashSale.expiresAt]);\n\n  if (!isVisible || timeLeft.expired) {\n    return null;\n  }\n\n  return (\n    <div className=\"bg-gradient-to-r from-red-500 to-pink-500 text-white p-4 relative\">\n      <div className=\"container mx-auto\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-4\">\n            <div className=\"flex items-center gap-2\">\n              <Zap className=\"w-5 h-5 animate-pulse\" />\n              <Badge className=\"bg-white/20 text-white border-white/30\">\n                PERSONALIZED FLASH SALE\n              </Badge>\n            </div>\n            \n            <div className=\"hidden md:block\">\n              <p className=\"text-sm opacity-90\">{flashSale.reason}</p>\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-4\">\n            <div className=\"text-center\">\n              <p className=\"font-semibold\">{flashSale.productName}</p>\n              <div className=\"flex items-center gap-2\">\n                <span className=\"line-through text-sm opacity-75\">\n                  {formatPrice(flashSale.originalPrice)}\n                </span>\n                <span className=\"font-bold text-lg\">\n                  {formatPrice(flashSale.salePrice)}\n                </span>\n                <Badge className=\"bg-yellow-400 text-black\">\n                  {flashSale.discount}% OFF\n                </Badge>\n              </div>\n            </div>\n\n            <div className=\"flex items-center gap-2 bg-white/20 rounded-lg px-3 py-2\">\n              <Clock className=\"w-4 h-4\" />\n              <div className=\"text-center\">\n                <div className=\"font-mono font-bold\">\n                  {String(timeLeft.hours).padStart(2, '0')}:\n                  {String(timeLeft.minutes).padStart(2, '0')}:\n                  {String(timeLeft.seconds).padStart(2, '0')}\n                </div>\n                <div className=\"text-xs opacity-75\">Time left</div>\n              </div>\n            </div>\n\n            <Button \n              className=\"bg-white text-red-500 hover:bg-gray-100 font-semibold\"\n              onClick={() => {\n                // In a real app, this would add the item to cart with the sale price\n                alert('Added to cart with flash sale price!');\n              }}\n            >\n              Claim Deal\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setIsVisible(false)}\n              className=\"text-white hover:bg-white/20\"\n            >\n              <X className=\"w-4 h-4\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile layout */}\n        <div className=\"md:hidden mt-3 space-y-2\">\n          <p className=\"text-sm opacity-90\">{flashSale.reason}</p>\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"font-semibold\">{flashSale.productName}</p>\n              <div className=\"flex items-center gap-2\">\n                <span className=\"line-through text-sm opacity-75\">\n                  {formatPrice(flashSale.originalPrice)}\n                </span>\n                <span className=\"font-bold\">\n                  {formatPrice(flashSale.salePrice)}\n                </span>\n                <Badge className=\"bg-yellow-400 text-black text-xs\">\n                  {flashSale.discount}% OFF\n                </Badge>\n              </div>\n            </div>\n            <Button \n              size=\"sm\"\n              className=\"bg-white text-red-500 hover:bg-gray-100 font-semibold\"\n              onClick={() => {\n                alert('Added to cart with flash sale price!');\n              }}\n            >\n              Claim Deal\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAmBO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,OAAO;QAAG,SAAS;QAAG,SAAS;QAAG,SAAS;IAAM;IAE5F,oCAAoC;IACpC,MAAM,YAAuB;QAC3B,IAAI;QACJ,aAAa;QACb,eAAe;QACf,WAAW;QACX,UAAU;QACV,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;QAC5D,QAAQ;QACR,gBAAgB;IAClB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,YAAY;YACxB,MAAM,OAAO,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,SAAS;YACnD,YAAY;YAEZ,IAAI,KAAK,OAAO,EAAE;gBAChB,aAAa;gBACb,cAAc;YAChB;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,UAAU,SAAS;KAAC;IAExB,IAAI,CAAC,aAAa,SAAS,OAAO,EAAE;QAClC,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAyC;;;;;;;;;;;;8CAK5D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAsB,UAAU,MAAM;;;;;;;;;;;;;;;;;sCAIvD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAiB,UAAU,WAAW;;;;;;sDACnD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,UAAU,aAAa;;;;;;8DAEtC,8OAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,UAAU,SAAS;;;;;;8DAElC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;;wDACd,UAAU,QAAQ;wDAAC;;;;;;;;;;;;;;;;;;;8CAK1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;wDACZ,OAAO,SAAS,KAAK,EAAE,QAAQ,CAAC,GAAG;wDAAK;wDACxC,OAAO,SAAS,OAAO,EAAE,QAAQ,CAAC,GAAG;wDAAK;wDAC1C,OAAO,SAAS,OAAO,EAAE,QAAQ,CAAC,GAAG;;;;;;;8DAExC,8OAAC;oDAAI,WAAU;8DAAqB;;;;;;;;;;;;;;;;;;8CAIxC,8OAAC,kIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS;wCACP,qEAAqE;wCACrE,MAAM;oCACR;8CACD;;;;;;8CAID,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAMnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAsB,UAAU,MAAM;;;;;;sCACnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAE,WAAU;sDAAiB,UAAU,WAAW;;;;;;sDACnD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,UAAU,aAAa;;;;;;8DAEtC,8OAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,UAAU,SAAS;;;;;;8DAElC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;;wDACd,UAAU,QAAQ;wDAAC;;;;;;;;;;;;;;;;;;;8CAI1B,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP,MAAM;oCACR;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}