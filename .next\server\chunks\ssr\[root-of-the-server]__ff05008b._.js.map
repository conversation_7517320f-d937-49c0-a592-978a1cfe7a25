{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price);\n}\n\nexport function formatRating(rating: number): string {\n  return rating.toFixed(1);\n}\n\nexport function getEcoRatingColor(rating: 'A' | 'B' | 'C' | 'D' | 'E'): string {\n  const colors = {\n    A: 'bg-green-500',\n    B: 'bg-lime-500',\n    C: 'bg-yellow-500',\n    D: 'bg-orange-500',\n    E: 'bg-red-500',\n  };\n  return colors[rating];\n}\n\nexport function getEcoRatingText(rating: 'A' | 'B' | 'C' | 'D' | 'E'): string {\n  const texts = {\n    A: 'Excellent',\n    B: 'Very Good',\n    C: 'Good',\n    D: 'Fair',\n    E: 'Poor',\n  };\n  return texts[rating];\n}\n\nexport function calculateCarbonFootprint(weight: number, distance: number): number {\n  // Simplified calculation: kg CO2 = weight (kg) * distance (km) * emission factor\n  const emissionFactor = 0.0001; // kg CO2 per kg per km (simplified)\n  return weight * distance * emissionFactor;\n}\n\nexport function debounce<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substr(0, maxLength) + '...';\n}\n\nexport function getTimeUntilExpiry(expiryDate: string): {\n  hours: number;\n  minutes: number;\n  seconds: number;\n  expired: boolean;\n} {\n  const now = new Date().getTime();\n  const expiry = new Date(expiryDate).getTime();\n  const difference = expiry - now;\n\n  if (difference <= 0) {\n    return { hours: 0, minutes: 0, seconds: 0, expired: true };\n  }\n\n  const hours = Math.floor(difference / (1000 * 60 * 60));\n  const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));\n  const seconds = Math.floor((difference % (1000 * 60)) / 1000);\n\n  return { hours, minutes, seconds, expired: false };\n}\n\nexport function getColorName(hex: string): string {\n  const colors: Record<string, string> = {\n    '#000000': 'Black',\n    '#FFFFFF': 'White',\n    '#FF0000': 'Red',\n    '#00FF00': 'Green',\n    '#0000FF': 'Blue',\n    '#FFFF00': 'Yellow',\n    '#FF00FF': 'Magenta',\n    '#00FFFF': 'Cyan',\n    '#FFA500': 'Orange',\n    '#800080': 'Purple',\n    '#FFC0CB': 'Pink',\n    '#A52A2A': 'Brown',\n    '#808080': 'Gray',\n    '#000080': 'Navy',\n    '#008000': 'Dark Green',\n    '#800000': 'Maroon',\n  };\n\n  return colors[hex.toUpperCase()] || hex;\n}\n\nexport function analyzeUserMood(recentActivity: Array<{ timeSpent: number; type: string }>): 'cheerful' | 'indecisive' | 'frustrated' | 'focused' {\n  // Simplified mood analysis based on user behavior\n  // In a real app, this would use more sophisticated AI\n\n  if (recentActivity.length === 0) return 'focused';\n\n  const quickDecisions = recentActivity.filter(a => a.timeSpent < 30).length;\n  const longBrowsing = recentActivity.filter(a => a.timeSpent > 300).length;\n  const backAndForth = recentActivity.filter(a => a.type === 'back_navigation').length;\n\n  if (quickDecisions > longBrowsing && backAndForth < 2) return 'cheerful';\n  if (longBrowsing > quickDecisions && backAndForth > 3) return 'indecisive';\n  if (backAndForth > 5) return 'frustrated';\n\n  return 'focused';\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,MAAc;IACzC,OAAO,OAAO,OAAO,CAAC;AACxB;AAEO,SAAS,kBAAkB,MAAmC;IACnE,MAAM,SAAS;QACb,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IACA,OAAO,MAAM,CAAC,OAAO;AACvB;AAEO,SAAS,iBAAiB,MAAmC;IAClE,MAAM,QAAQ;QACZ,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IACA,OAAO,KAAK,CAAC,OAAO;AACtB;AAEO,SAAS,yBAAyB,MAAc,EAAE,QAAgB;IACvE,iFAAiF;IACjF,MAAM,iBAAiB,QAAQ,oCAAoC;IACnE,OAAO,SAAS,WAAW;AAC7B;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,MAAM,CAAC,GAAG,aAAa;AACrC;AAEO,SAAS,mBAAmB,UAAkB;IAMnD,MAAM,MAAM,IAAI,OAAO,OAAO;IAC9B,MAAM,SAAS,IAAI,KAAK,YAAY,OAAO;IAC3C,MAAM,aAAa,SAAS;IAE5B,IAAI,cAAc,GAAG;QACnB,OAAO;YAAE,OAAO;YAAG,SAAS;YAAG,SAAS;YAAG,SAAS;QAAK;IAC3D;IAEA,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa,CAAC,OAAO,KAAK,EAAE;IACrD,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,aAAa,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IACvE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,aAAa,CAAC,OAAO,EAAE,IAAK;IAExD,OAAO;QAAE;QAAO;QAAS;QAAS,SAAS;IAAM;AACnD;AAEO,SAAS,aAAa,GAAW;IACtC,MAAM,SAAiC;QACrC,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;IACb;IAEA,OAAO,MAAM,CAAC,IAAI,WAAW,GAAG,IAAI;AACtC;AAEO,SAAS,gBAAgB,cAA0D;IACxF,kDAAkD;IAClD,sDAAsD;IAEtD,IAAI,eAAe,MAAM,KAAK,GAAG,OAAO;IAExC,MAAM,iBAAiB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,GAAG,IAAI,MAAM;IAC1E,MAAM,eAAe,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,GAAG,KAAK,MAAM;IACzE,MAAM,eAAe,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,mBAAmB,MAAM;IAEpF,IAAI,iBAAiB,gBAAgB,eAAe,GAAG,OAAO;IAC9D,IAAI,eAAe,kBAAkB,eAAe,GAAG,OAAO;IAC9D,IAAI,eAAe,GAAG,OAAO;IAE7B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\";\n    \n    const variants = {\n      default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n      destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n      outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n      secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n      ghost: \"hover:bg-accent hover:text-accent-foreground\",\n      link: \"text-primary underline-offset-4 hover:underline\",\n    };\n\n    const sizes = {\n      default: \"h-10 px-4 py-2\",\n      sm: \"h-9 rounded-md px-3\",\n      lg: \"h-11 rounded-md px-8\",\n      icon: \"h-10 w-10\",\n    };\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = \"Button\";\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,aAAa;QACb,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'success';\n}\n\nfunction Badge({ className, variant = 'default', ...props }: BadgeProps) {\n  const variants = {\n    default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n    secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n    destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n    outline: \"text-foreground\",\n    success: \"border-transparent bg-green-500 text-white hover:bg-green-500/80\",\n  };\n\n  return (\n    <div\n      className={cn(\n        \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n        variants[variant],\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Badge };\n"], "names": [], "mappings": ";;;;AACA;;;AAOA,SAAS,MAAM,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAmB;IACrE,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,aAAa;QACb,SAAS;QACT,SAAS;IACX;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0KACA,QAAQ,CAAC,QAAQ,EACjB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/store/cart.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist, createJSONStorage } from 'zustand/middleware';\n\nexport interface Product {\n  id: string;\n  name: string;\n  price: number;\n  images: string[];\n  colors: Array<{\n    name: string;\n    value: string;\n    image: string;\n  }>;\n  sizes: string[];\n  description: string;\n  category: string;\n  inStock: boolean;\n  rating: number;\n  reviews: number;\n  ecoRating?: 'A' | 'B' | 'C' | 'D' | 'E';\n  carbonFootprint?: number; // in kg CO2\n}\n\nexport interface CartItem {\n  product: Product;\n  selectedColor: string;\n  selectedSize: string;\n  quantity: number;\n}\n\ninterface CartStore {\n  items: CartItem[];\n  isOpen: boolean;\n  addItem: (product: Product, color: string, size: string, quantity?: number) => void;\n  removeItem: (productId: string, color: string, size: string) => void;\n  updateQuantity: (productId: string, color: string, size: string, quantity: number) => void;\n  clearCart: () => void;\n  getTotalItems: () => number;\n  getTotalPrice: () => number;\n  setIsOpen: (open: boolean) => void;\n  mergeGuestCart: (guestItems: CartItem[]) => void;\n}\n\nexport const useCartStore = create<CartStore>()(\n  persist(\n    (set, get) => ({\n      items: [],\n      isOpen: false,\n      \n      addItem: (product, color, size, quantity = 1) => {\n        const items = get().items;\n        const existingItemIndex = items.findIndex(\n          item => \n            item.product.id === product.id && \n            item.selectedColor === color && \n            item.selectedSize === size\n        );\n\n        if (existingItemIndex > -1) {\n          const updatedItems = [...items];\n          updatedItems[existingItemIndex].quantity += quantity;\n          set({ items: updatedItems });\n        } else {\n          set({ \n            items: [...items, { \n              product, \n              selectedColor: color, \n              selectedSize: size, \n              quantity \n            }] \n          });\n        }\n      },\n\n      removeItem: (productId, color, size) => {\n        set({\n          items: get().items.filter(\n            item => !(\n              item.product.id === productId && \n              item.selectedColor === color && \n              item.selectedSize === size\n            )\n          )\n        });\n      },\n\n      updateQuantity: (productId, color, size, quantity) => {\n        if (quantity <= 0) {\n          get().removeItem(productId, color, size);\n          return;\n        }\n\n        const items = get().items;\n        const updatedItems = items.map(item => {\n          if (\n            item.product.id === productId && \n            item.selectedColor === color && \n            item.selectedSize === size\n          ) {\n            return { ...item, quantity };\n          }\n          return item;\n        });\n        set({ items: updatedItems });\n      },\n\n      clearCart: () => set({ items: [] }),\n\n      getTotalItems: () => {\n        return get().items.reduce((total, item) => total + item.quantity, 0);\n      },\n\n      getTotalPrice: () => {\n        return get().items.reduce(\n          (total, item) => total + (item.product.price * item.quantity), \n          0\n        );\n      },\n\n      setIsOpen: (open) => set({ isOpen: open }),\n\n      mergeGuestCart: (guestItems) => {\n        const currentItems = get().items;\n        const mergedItems = [...currentItems];\n\n        guestItems.forEach(guestItem => {\n          const existingIndex = mergedItems.findIndex(\n            item => \n              item.product.id === guestItem.product.id && \n              item.selectedColor === guestItem.selectedColor && \n              item.selectedSize === guestItem.selectedSize\n          );\n\n          if (existingIndex > -1) {\n            mergedItems[existingIndex].quantity += guestItem.quantity;\n          } else {\n            mergedItems.push(guestItem);\n          }\n        });\n\n        set({ items: mergedItems });\n      },\n    }),\n    {\n      name: 'cart-storage',\n      storage: createJSONStorage(() => localStorage),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA0CO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,OAAO,EAAE;QACT,QAAQ;QAER,SAAS,CAAC,SAAS,OAAO,MAAM,WAAW,CAAC;YAC1C,MAAM,QAAQ,MAAM,KAAK;YACzB,MAAM,oBAAoB,MAAM,SAAS,CACvC,CAAA,OACE,KAAK,OAAO,CAAC,EAAE,KAAK,QAAQ,EAAE,IAC9B,KAAK,aAAa,KAAK,SACvB,KAAK,YAAY,KAAK;YAG1B,IAAI,oBAAoB,CAAC,GAAG;gBAC1B,MAAM,eAAe;uBAAI;iBAAM;gBAC/B,YAAY,CAAC,kBAAkB,CAAC,QAAQ,IAAI;gBAC5C,IAAI;oBAAE,OAAO;gBAAa;YAC5B,OAAO;gBACL,IAAI;oBACF,OAAO;2BAAI;wBAAO;4BAChB;4BACA,eAAe;4BACf,cAAc;4BACd;wBACF;qBAAE;gBACJ;YACF;QACF;QAEA,YAAY,CAAC,WAAW,OAAO;YAC7B,IAAI;gBACF,OAAO,MAAM,KAAK,CAAC,MAAM,CACvB,CAAA,OAAQ,CAAC,CACP,KAAK,OAAO,CAAC,EAAE,KAAK,aACpB,KAAK,aAAa,KAAK,SACvB,KAAK,YAAY,KAAK,IACxB;YAEJ;QACF;QAEA,gBAAgB,CAAC,WAAW,OAAO,MAAM;YACvC,IAAI,YAAY,GAAG;gBACjB,MAAM,UAAU,CAAC,WAAW,OAAO;gBACnC;YACF;YAEA,MAAM,QAAQ,MAAM,KAAK;YACzB,MAAM,eAAe,MAAM,GAAG,CAAC,CAAA;gBAC7B,IACE,KAAK,OAAO,CAAC,EAAE,KAAK,aACpB,KAAK,aAAa,KAAK,SACvB,KAAK,YAAY,KAAK,MACtB;oBACA,OAAO;wBAAE,GAAG,IAAI;wBAAE;oBAAS;gBAC7B;gBACA,OAAO;YACT;YACA,IAAI;gBAAE,OAAO;YAAa;QAC5B;QAEA,WAAW,IAAM,IAAI;gBAAE,OAAO,EAAE;YAAC;QAEjC,eAAe;YACb,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;QACpE;QAEA,eAAe;YACb,OAAO,MAAM,KAAK,CAAC,MAAM,CACvB,CAAC,OAAO,OAAS,QAAS,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAC5D;QAEJ;QAEA,WAAW,CAAC,OAAS,IAAI;gBAAE,QAAQ;YAAK;QAExC,gBAAgB,CAAC;YACf,MAAM,eAAe,MAAM,KAAK;YAChC,MAAM,cAAc;mBAAI;aAAa;YAErC,WAAW,OAAO,CAAC,CAAA;gBACjB,MAAM,gBAAgB,YAAY,SAAS,CACzC,CAAA,OACE,KAAK,OAAO,CAAC,EAAE,KAAK,UAAU,OAAO,CAAC,EAAE,IACxC,KAAK,aAAa,KAAK,UAAU,aAAa,IAC9C,KAAK,YAAY,KAAK,UAAU,YAAY;gBAGhD,IAAI,gBAAgB,CAAC,GAAG;oBACtB,WAAW,CAAC,cAAc,CAAC,QAAQ,IAAI,UAAU,QAAQ;gBAC3D,OAAO;oBACL,YAAY,IAAI,CAAC;gBACnB;YACF;YAEA,IAAI;gBAAE,OAAO;YAAY;QAC3B;IACF,CAAC,GACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;AACnC", "debugId": null}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/cart/cart-button.tsx"], "sourcesContent": ["'use client';\n\nimport { ShoppingBag } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { useCartStore } from '@/store/cart';\nimport { cn } from '@/lib/utils';\n\ninterface CartButtonProps {\n  className?: string;\n}\n\nexport function CartButton({ className }: CartButtonProps) {\n  const { getTotalItems, setIsOpen } = useCartStore();\n  const itemCount = getTotalItems();\n\n  return (\n    <Button\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"relative\", className)}\n      onClick={() => setIsOpen(true)}\n    >\n      <ShoppingBag className=\"w-6 h-6\" />\n      {itemCount > 0 && (\n        <Badge \n          className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs bg-primary text-primary-foreground\"\n        >\n          {itemCount > 99 ? '99+' : itemCount}\n        </Badge>\n      )}\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAYO,SAAS,WAAW,EAAE,SAAS,EAAmB;IACvD,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD;IAChD,MAAM,YAAY;IAElB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,SAAS,IAAM,UAAU;;0BAEzB,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YACtB,YAAY,mBACX,8OAAC,iIAAA,CAAA,QAAK;gBACJ,WAAU;0BAET,YAAY,KAAK,QAAQ;;;;;;;;;;;;AAKpC", "debugId": null}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/layout/header.tsx"], "sourcesContent": ["'use client';\n\nimport { Search, User, Heart, Menu } from 'lucide-react';\nimport { <PERSON><PERSON> } from '@/components/ui/button';\nimport { CartButton } from '@/components/cart/cart-button';\n\nexport function Header() {\n  return (\n    <header className=\"sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Mobile Menu */}\n          <Button variant=\"ghost\" size=\"icon\" className=\"md:hidden\">\n            <Menu className=\"w-5 h-5\" />\n          </Button>\n\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <h1 className=\"text-xl font-bold\">NextGen Store</h1>\n          </div>\n\n          {/* Navigation - Desktop */}\n          <nav className=\"hidden md:flex items-center space-x-6\">\n            <a href=\"#\" className=\"text-sm font-medium hover:text-primary transition-colors\">\n              New Arrivals\n            </a>\n            <a href=\"#\" className=\"text-sm font-medium hover:text-primary transition-colors\">\n              Clothing\n            </a>\n            <a href=\"#\" className=\"text-sm font-medium hover:text-primary transition-colors\">\n              Electronics\n            </a>\n            <a href=\"#\" className=\"text-sm font-medium hover:text-primary transition-colors\">\n              Accessories\n            </a>\n            <a href=\"#\" className=\"text-sm font-medium hover:text-primary transition-colors\">\n              Sustainable\n            </a>\n          </nav>\n\n          {/* Search Bar - Desktop */}\n          <div className=\"hidden md:flex items-center flex-1 max-w-md mx-6\">\n            <div className=\"relative w-full\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search products...\"\n                className=\"w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent\"\n              />\n            </div>\n          </div>\n\n          {/* Actions */}\n          <div className=\"flex items-center space-x-2\">\n            {/* Search - Mobile */}\n            <Button variant=\"ghost\" size=\"icon\" className=\"md:hidden\">\n              <Search className=\"w-5 h-5\" />\n            </Button>\n\n            {/* Wishlist */}\n            <Button variant=\"ghost\" size=\"icon\">\n              <Heart className=\"w-5 h-5\" />\n            </Button>\n\n            {/* Account */}\n            <Button variant=\"ghost\" size=\"icon\">\n              <User className=\"w-5 h-5\" />\n            </Button>\n\n            {/* Cart */}\n            <CartButton />\n          </div>\n        </div>\n\n        {/* Mobile Search */}\n        <div className=\"md:hidden pb-4\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search products...\"\n              className=\"w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent\"\n            />\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AAJA;;;;;AAMO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAO,WAAU;sCAC5C,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAIlB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAoB;;;;;;;;;;;sCAIpC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAA2D;;;;;;8CAGjF,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAA2D;;;;;;8CAGjF,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAA2D;;;;;;8CAGjF,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAA2D;;;;;;8CAGjF,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAA2D;;;;;;;;;;;;sCAMnF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,WAAU;8CAC5C,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAIpB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAInB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAIlB,8OAAC,4IAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;8BAKf,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB", "debugId": null}}, {"offset": {"line": 630, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/cart/cart-sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { X, Plus, Minus, Trash2 } from 'lucide-react';\nimport Image from 'next/image';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { useCartStore } from '@/store/cart';\nimport { formatPrice } from '@/lib/utils';\n\n\nexport function CartSidebar() {\n  const {\n    items,\n    isOpen,\n    setIsOpen,\n    updateQuantity,\n    removeItem,\n    getTotalPrice,\n    clearCart\n  } = useCartStore();\n\n  if (!isOpen) return null;\n\n  return (\n    <>\n      {/* Backdrop */}\n      <div\n        className=\"fixed inset-0 bg-black/50 z-40\"\n        onClick={() => setIsOpen(false)}\n      />\n\n      {/* Sidebar */}\n      <div className=\"fixed right-0 top-0 h-full w-full max-w-md bg-white z-50 shadow-xl flex flex-col\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 border-b\">\n          <h2 className=\"text-lg font-semibold\">Shopping Cart</h2>\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={() => setIsOpen(false)}\n          >\n            <X className=\"w-5 h-5\" />\n          </Button>\n        </div>\n\n        {/* Cart Items */}\n        <div className=\"flex-1 overflow-y-auto p-4\">\n          {items.length === 0 ? (\n            <div className=\"flex flex-col items-center justify-center h-full text-center\">\n              <div className=\"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4\">\n                <svg className=\"w-12 h-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-medium mb-2\">Your cart is empty</h3>\n              <p className=\"text-gray-500 mb-4\">Add some items to get started</p>\n              <Button onClick={() => setIsOpen(false)}>\n                Continue Shopping\n              </Button>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {items.map((item) => (\n                <div key={`${item.product.id}-${item.selectedColor}-${item.selectedSize}`} className=\"flex gap-3 p-3 border rounded-lg\">\n                  {/* Product Image */}\n                  <div className=\"relative w-16 h-16 flex-shrink-0\">\n                    <Image\n                      src={item.product.images[0]}\n                      alt={item.product.name}\n                      fill\n                      className=\"object-cover rounded\"\n                    />\n                  </div>\n\n                  {/* Product Details */}\n                  <div className=\"flex-1 min-w-0\">\n                    <h4 className=\"font-medium text-sm truncate\">\n                      {item.product.name}\n                    </h4>\n                    <div className=\"flex items-center gap-2 mt-1\">\n                      <div\n                        className=\"w-3 h-3 rounded-full border\"\n                        style={{ backgroundColor: item.product.colors.find(c => c.name === item.selectedColor)?.value }}\n                      />\n                      <span className=\"text-xs text-gray-500\">\n                        {item.selectedColor}\n                      </span>\n                      <Badge variant=\"outline\" className=\"text-xs\">\n                        {item.selectedSize}\n                      </Badge>\n                    </div>\n                    <div className=\"flex items-center justify-between mt-2\">\n                      <span className=\"font-semibold text-sm\">\n                        {formatPrice(item.product.price)}\n                      </span>\n\n                      {/* Quantity Controls */}\n                      <div className=\"flex items-center gap-1\">\n                        <Button\n                          variant=\"outline\"\n                          size=\"icon\"\n                          className=\"w-6 h-6\"\n                          onClick={() => updateQuantity(\n                            item.product.id,\n                            item.selectedColor,\n                            item.selectedSize,\n                            item.quantity - 1\n                          )}\n                        >\n                          <Minus className=\"w-3 h-3\" />\n                        </Button>\n                        <span className=\"w-8 text-center text-sm\">\n                          {item.quantity}\n                        </span>\n                        <Button\n                          variant=\"outline\"\n                          size=\"icon\"\n                          className=\"w-6 h-6\"\n                          onClick={() => updateQuantity(\n                            item.product.id,\n                            item.selectedColor,\n                            item.selectedSize,\n                            item.quantity + 1\n                          )}\n                        >\n                          <Plus className=\"w-3 h-3\" />\n                        </Button>\n                        <Button\n                          variant=\"ghost\"\n                          size=\"icon\"\n                          className=\"w-6 h-6 text-red-500 hover:text-red-700\"\n                          onClick={() => removeItem(\n                            item.product.id,\n                            item.selectedColor,\n                            item.selectedSize\n                          )}\n                        >\n                          <Trash2 className=\"w-3 h-3\" />\n                        </Button>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* Footer */}\n        {items.length > 0 && (\n          <div className=\"border-t p-4 space-y-4\">\n            {/* Clear Cart */}\n            <Button\n              variant=\"outline\"\n              className=\"w-full\"\n              onClick={clearCart}\n            >\n              Clear Cart\n            </Button>\n\n            {/* Total */}\n            <div className=\"flex items-center justify-between text-lg font-semibold\">\n              <span>Total:</span>\n              <span>{formatPrice(getTotalPrice())}</span>\n            </div>\n\n            {/* Checkout Button */}\n            <Button className=\"w-full\" size=\"lg\">\n              Checkout\n            </Button>\n          </div>\n        )}\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAUO,SAAS;IACd,MAAM,EACJ,KAAK,EACL,MAAM,EACN,SAAS,EACT,cAAc,EACd,UAAU,EACV,aAAa,EACb,SAAS,EACV,GAAG,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD;IAEf,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE;;0BAEE,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;0BAI3B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,UAAU;0CAEzB,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;kCACZ,MAAM,MAAM,KAAK,kBAChB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAA0B,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjF,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,8OAAC;oCAAG,WAAU;8CAA2B;;;;;;8CACzC,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,UAAU;8CAAQ;;;;;;;;;;;iDAK3C,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;oCAA0E,WAAU;;sDAEnF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,OAAO,CAAC,MAAM,CAAC,EAAE;gDAC3B,KAAK,KAAK,OAAO,CAAC,IAAI;gDACtB,IAAI;gDACJ,WAAU;;;;;;;;;;;sDAKd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,KAAK,OAAO,CAAC,IAAI;;;;;;8DAEpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,iBAAiB,KAAK,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,KAAK,aAAa,GAAG;4DAAM;;;;;;sEAEhG,8OAAC;4DAAK,WAAU;sEACb,KAAK,aAAa;;;;;;sEAErB,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAChC,KAAK,YAAY;;;;;;;;;;;;8DAGtB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,OAAO,CAAC,KAAK;;;;;;sEAIjC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,eACb,KAAK,OAAO,CAAC,EAAE,EACf,KAAK,aAAa,EAClB,KAAK,YAAY,EACjB,KAAK,QAAQ,GAAG;8EAGlB,cAAA,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;8EAEnB,8OAAC;oEAAK,WAAU;8EACb,KAAK,QAAQ;;;;;;8EAEhB,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,eACb,KAAK,OAAO,CAAC,EAAE,EACf,KAAK,aAAa,EAClB,KAAK,YAAY,EACjB,KAAK,QAAQ,GAAG;8EAGlB,cAAA,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,WACb,KAAK,OAAO,CAAC,EAAE,EACf,KAAK,aAAa,EAClB,KAAK,YAAY;8EAGnB,cAAA,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCA1ElB,GAAG,KAAK,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,YAAY,EAAE;;;;;;;;;;;;;;;oBAsFhF,MAAM,MAAM,GAAG,mBACd,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;0CACV;;;;;;0CAKD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAM,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;0CAIrB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,WAAU;gCAAS,MAAK;0CAAK;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/ai/ai-chatbot.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { MessageCircle, X, Send, Bot, User, Sparkles } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { analyzeUserMood } from '@/lib/utils';\n\ninterface Message {\n  id: string;\n  type: 'user' | 'bot';\n  content: string;\n  timestamp: Date;\n  mood?: 'cheerful' | 'indecisive' | 'frustrated' | 'focused';\n  recommendations?: Array<{\n    productId: string;\n    reason: string;\n  }>;\n}\n\nexport function AIChatbot() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: '1',\n      type: 'bot',\n      content: \"Hi! I'm your AI personal shopper. I can sense you're browsing with a focused mindset today. How can I help you find the perfect products?\",\n      timestamp: new Date(),\n      mood: 'focused'\n    }\n  ]);\n  const [inputValue, setInputValue] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const getMoodColor = (mood: string) => {\n    const colors = {\n      cheerful: 'bg-yellow-100 text-yellow-800',\n      indecisive: 'bg-blue-100 text-blue-800',\n      frustrated: 'bg-red-100 text-red-800',\n      focused: 'bg-green-100 text-green-800'\n    };\n    return colors[mood as keyof typeof colors] || 'bg-gray-100 text-gray-800';\n  };\n\n  const generateBotResponse = (userMessage: string): string => {\n    const lowerMessage = userMessage.toLowerCase();\n    \n    // Simple AI responses based on keywords\n    if (lowerMessage.includes('sustainable') || lowerMessage.includes('eco')) {\n      return \"Great choice! I love that you're thinking sustainably. Our Eco-Friendly Cotton T-Shirt has an 'A' eco-rating and only 2.1kg CO₂ footprint. It's made from 100% organic cotton and ethically manufactured. Would you like to see more sustainable options?\";\n    }\n    \n    if (lowerMessage.includes('budget') || lowerMessage.includes('cheap') || lowerMessage.includes('affordable')) {\n      return \"I understand you're looking for great value! Our Eco-Friendly Cotton T-Shirt at $29.99 offers excellent quality for the price. It's sustainable, comfortable, and versatile. For accessories, the Minimalist Leather Wallet at $49.99 is also a great investment piece.\";\n    }\n    \n    if (lowerMessage.includes('gift') || lowerMessage.includes('present')) {\n      return \"Perfect! Gift shopping can be tricky, but I'm here to help. What's the occasion and who is it for? For tech lovers, our Wireless Bluetooth Headphones are popular. For fashion-conscious friends, the Premium Denim Jeans are timeless. Tell me more about the recipient!\";\n    }\n    \n    if (lowerMessage.includes('electronics') || lowerMessage.includes('headphones') || lowerMessage.includes('tech')) {\n      return \"Excellent choice! Our Wireless Bluetooth Headphones are premium quality with active noise cancellation and 30-hour battery life. They have a 4.7-star rating and come in Black, White, or Silver. The audio quality is crystal-clear - perfect for music lovers and professionals.\";\n    }\n    \n    if (lowerMessage.includes('clothing') || lowerMessage.includes('shirt') || lowerMessage.includes('jeans')) {\n      return \"I can help you find the perfect clothing! Are you looking for casual wear like our organic cotton t-shirt, or something more structured like our premium denim jeans? What's your style preference and what occasions will you be wearing them for?\";\n    }\n    \n    if (lowerMessage.includes('confused') || lowerMessage.includes('help') || lowerMessage.includes('don\\'t know')) {\n      return \"No worries at all! Let me help you narrow things down. What are you shopping for today? Are you looking for: 1) Something for yourself or as a gift? 2) Clothing, electronics, or accessories? 3) Any specific budget range? I'll guide you to the perfect products!\";\n    }\n    \n    // Default responses\n    const defaultResponses = [\n      \"That's interesting! Based on your browsing pattern, I think you might like our curated selection. What type of products are you most interested in today?\",\n      \"I'm here to help you find exactly what you need! Can you tell me more about what you're looking for?\",\n      \"Great question! Let me think about the best recommendations for you. What's most important to you - quality, price, sustainability, or style?\",\n    ];\n    \n    return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];\n  };\n\n  const handleSendMessage = async () => {\n    if (!inputValue.trim()) return;\n\n    const userMessage: Message = {\n      id: Date.now().toString(),\n      type: 'user',\n      content: inputValue,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputValue('');\n    setIsTyping(true);\n\n    // Simulate AI thinking time\n    setTimeout(() => {\n      const botResponse: Message = {\n        id: (Date.now() + 1).toString(),\n        type: 'bot',\n        content: generateBotResponse(inputValue),\n        timestamp: new Date(),\n        mood: analyzeUserMood([{ timeSpent: 120, type: 'message' }])\n      };\n\n      setMessages(prev => [...prev, botResponse]);\n      setIsTyping(false);\n    }, 1000 + Math.random() * 2000);\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  if (!isOpen) {\n    return (\n      <Button\n        onClick={() => setIsOpen(true)}\n        className=\"fixed bottom-6 right-6 h-14 w-14 rounded-full shadow-lg bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 z-50\"\n        size=\"icon\"\n      >\n        <MessageCircle className=\"w-6 h-6\" />\n      </Button>\n    );\n  }\n\n  return (\n    <div className=\"fixed bottom-6 right-6 w-80 h-96 bg-white rounded-lg shadow-xl border z-50 flex flex-col\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between p-4 border-b bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-t-lg\">\n        <div className=\"flex items-center gap-2\">\n          <div className=\"w-8 h-8 bg-white/20 rounded-full flex items-center justify-center\">\n            <Bot className=\"w-5 h-5\" />\n          </div>\n          <div>\n            <h3 className=\"font-semibold\">AI Personal Shopper</h3>\n            <p className=\"text-xs opacity-90\">Emotion-aware assistant</p>\n          </div>\n        </div>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          onClick={() => setIsOpen(false)}\n          className=\"text-white hover:bg-white/20\"\n        >\n          <X className=\"w-4 h-4\" />\n        </Button>\n      </div>\n\n      {/* Messages */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n        {messages.map((message) => (\n          <div\n            key={message.id}\n            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}\n          >\n            <div\n              className={`max-w-[80%] rounded-lg p-3 ${\n                message.type === 'user'\n                  ? 'bg-purple-500 text-white'\n                  : 'bg-gray-100 text-gray-800'\n              }`}\n            >\n              <div className=\"flex items-center gap-2 mb-1\">\n                {message.type === 'bot' ? (\n                  <Bot className=\"w-4 h-4\" />\n                ) : (\n                  <User className=\"w-4 h-4\" />\n                )}\n                {message.mood && (\n                  <Badge className={`text-xs ${getMoodColor(message.mood)}`}>\n                    <Sparkles className=\"w-3 h-3 mr-1\" />\n                    {message.mood}\n                  </Badge>\n                )}\n              </div>\n              <p className=\"text-sm\">{message.content}</p>\n              <p className=\"text-xs opacity-70 mt-1\">\n                {message.timestamp.toLocaleTimeString([], { \n                  hour: '2-digit', \n                  minute: '2-digit' \n                })}\n              </p>\n            </div>\n          </div>\n        ))}\n        \n        {isTyping && (\n          <div className=\"flex justify-start\">\n            <div className=\"bg-gray-100 rounded-lg p-3 max-w-[80%]\">\n              <div className=\"flex items-center gap-2\">\n                <Bot className=\"w-4 h-4\" />\n                <div className=\"flex space-x-1\">\n                  <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                  <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                  <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input */}\n      <div className=\"p-4 border-t\">\n        <div className=\"flex gap-2\">\n          <input\n            type=\"text\"\n            value={inputValue}\n            onChange={(e) => setInputValue(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Ask me anything...\"\n            className=\"flex-1 px-3 py-2 border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500\"\n          />\n          <Button\n            onClick={handleSendMessage}\n            disabled={!inputValue.trim() || isTyping}\n            size=\"icon\"\n            className=\"bg-purple-500 hover:bg-purple-600\"\n          >\n            <Send className=\"w-4 h-4\" />\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAoBO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW,IAAI;YACf,MAAM;QACR;KACD;IACD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,eAAe,CAAC;QACpB,MAAM,SAAS;YACb,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,SAAS;QACX;QACA,OAAO,MAAM,CAAC,KAA4B,IAAI;IAChD;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,eAAe,YAAY,WAAW;QAE5C,wCAAwC;QACxC,IAAI,aAAa,QAAQ,CAAC,kBAAkB,aAAa,QAAQ,CAAC,QAAQ;YACxE,OAAO;QACT;QAEA,IAAI,aAAa,QAAQ,CAAC,aAAa,aAAa,QAAQ,CAAC,YAAY,aAAa,QAAQ,CAAC,eAAe;YAC5G,OAAO;QACT;QAEA,IAAI,aAAa,QAAQ,CAAC,WAAW,aAAa,QAAQ,CAAC,YAAY;YACrE,OAAO;QACT;QAEA,IAAI,aAAa,QAAQ,CAAC,kBAAkB,aAAa,QAAQ,CAAC,iBAAiB,aAAa,QAAQ,CAAC,SAAS;YAChH,OAAO;QACT;QAEA,IAAI,aAAa,QAAQ,CAAC,eAAe,aAAa,QAAQ,CAAC,YAAY,aAAa,QAAQ,CAAC,UAAU;YACzG,OAAO;QACT;QAEA,IAAI,aAAa,QAAQ,CAAC,eAAe,aAAa,QAAQ,CAAC,WAAW,aAAa,QAAQ,CAAC,gBAAgB;YAC9G,OAAO;QACT;QAEA,oBAAoB;QACpB,MAAM,mBAAmB;YACvB;YACA;YACA;SACD;QAED,OAAO,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;IAC9E;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS;YACT,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,cAAc;QACd,YAAY;QAEZ,4BAA4B;QAC5B,WAAW;YACT,MAAM,cAAuB;gBAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS,oBAAoB;gBAC7B,WAAW,IAAI;gBACf,MAAM,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE;oBAAC;wBAAE,WAAW;wBAAK,MAAM;oBAAU;iBAAE;YAC7D;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAY;YAC1C,YAAY;QACd,GAAG,OAAO,KAAK,MAAM,KAAK;IAC5B;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC,kIAAA,CAAA,SAAM;YACL,SAAS,IAAM,UAAU;YACzB,WAAU;YACV,MAAK;sBAEL,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;;;;;;IAG/B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAEjB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAgB;;;;;;kDAC9B,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;;;;;;;kCAGtC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,UAAU;wBACzB,WAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKjB,8OAAC;gBAAI,WAAU;;oBACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4BAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;sCAE9E,cAAA,8OAAC;gCACC,WAAW,CAAC,2BAA2B,EACrC,QAAQ,IAAI,KAAK,SACb,6BACA,6BACJ;;kDAEF,8OAAC;wCAAI,WAAU;;4CACZ,QAAQ,IAAI,KAAK,sBAChB,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;qEAEf,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAEjB,QAAQ,IAAI,kBACX,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAW,CAAC,QAAQ,EAAE,aAAa,QAAQ,IAAI,GAAG;;kEACvD,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDACnB,QAAQ,IAAI;;;;;;;;;;;;;kDAInB,8OAAC;wCAAE,WAAU;kDAAW,QAAQ,OAAO;;;;;;kDACvC,8OAAC;wCAAE,WAAU;kDACV,QAAQ,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE;4CACxC,MAAM;4CACN,QAAQ;wCACV;;;;;;;;;;;;2BA5BC,QAAQ,EAAE;;;;;oBAkClB,0BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;gDAAkD,OAAO;oDAAE,gBAAgB;gDAAO;;;;;;0DACjG,8OAAC;gDAAI,WAAU;gDAAkD,OAAO;oDAAE,gBAAgB;gDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM3G,8OAAC;wBAAI,KAAK;;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,YAAY;4BACZ,aAAY;4BACZ,WAAU;;;;;;sCAEZ,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC,WAAW,IAAI,MAAM;4BAChC,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B", "debugId": null}}]}