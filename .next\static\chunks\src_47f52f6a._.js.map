{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price);\n}\n\nexport function formatRating(rating: number): string {\n  return rating.toFixed(1);\n}\n\nexport function getEcoRatingColor(rating: 'A' | 'B' | 'C' | 'D' | 'E'): string {\n  const colors = {\n    A: 'bg-green-500',\n    B: 'bg-lime-500',\n    C: 'bg-yellow-500',\n    D: 'bg-orange-500',\n    E: 'bg-red-500',\n  };\n  return colors[rating];\n}\n\nexport function getEcoRatingText(rating: 'A' | 'B' | 'C' | 'D' | 'E'): string {\n  const texts = {\n    A: 'Excellent',\n    B: 'Very Good',\n    C: 'Good',\n    D: 'Fair',\n    E: 'Poor',\n  };\n  return texts[rating];\n}\n\nexport function calculateCarbonFootprint(weight: number, distance: number): number {\n  // Simplified calculation: kg CO2 = weight (kg) * distance (km) * emission factor\n  const emissionFactor = 0.0001; // kg CO2 per kg per km (simplified)\n  return weight * distance * emissionFactor;\n}\n\nexport function debounce<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substr(0, maxLength) + '...';\n}\n\nexport function getTimeUntilExpiry(expiryDate: string): {\n  hours: number;\n  minutes: number;\n  seconds: number;\n  expired: boolean;\n} {\n  const now = new Date().getTime();\n  const expiry = new Date(expiryDate).getTime();\n  const difference = expiry - now;\n\n  if (difference <= 0) {\n    return { hours: 0, minutes: 0, seconds: 0, expired: true };\n  }\n\n  const hours = Math.floor(difference / (1000 * 60 * 60));\n  const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));\n  const seconds = Math.floor((difference % (1000 * 60)) / 1000);\n\n  return { hours, minutes, seconds, expired: false };\n}\n\nexport function getColorName(hex: string): string {\n  const colors: Record<string, string> = {\n    '#000000': 'Black',\n    '#FFFFFF': 'White',\n    '#FF0000': 'Red',\n    '#00FF00': 'Green',\n    '#0000FF': 'Blue',\n    '#FFFF00': 'Yellow',\n    '#FF00FF': 'Magenta',\n    '#00FFFF': 'Cyan',\n    '#FFA500': 'Orange',\n    '#800080': 'Purple',\n    '#FFC0CB': 'Pink',\n    '#A52A2A': 'Brown',\n    '#808080': 'Gray',\n    '#000080': 'Navy',\n    '#008000': 'Dark Green',\n    '#800000': 'Maroon',\n  };\n\n  return colors[hex.toUpperCase()] || hex;\n}\n\nexport function analyzeUserMood(recentActivity: Array<{ timeSpent: number; type: string }>): 'cheerful' | 'indecisive' | 'frustrated' | 'focused' {\n  // Simplified mood analysis based on user behavior\n  // In a real app, this would use more sophisticated AI\n\n  if (recentActivity.length === 0) return 'focused';\n\n  const quickDecisions = recentActivity.filter(a => a.timeSpent < 30).length;\n  const longBrowsing = recentActivity.filter(a => a.timeSpent > 300).length;\n  const backAndForth = recentActivity.filter(a => a.type === 'back_navigation').length;\n\n  if (quickDecisions > longBrowsing && backAndForth < 2) return 'cheerful';\n  if (longBrowsing > quickDecisions && backAndForth > 3) return 'indecisive';\n  if (backAndForth > 5) return 'frustrated';\n\n  return 'focused';\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,MAAc;IACzC,OAAO,OAAO,OAAO,CAAC;AACxB;AAEO,SAAS,kBAAkB,MAAmC;IACnE,MAAM,SAAS;QACb,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IACA,OAAO,MAAM,CAAC,OAAO;AACvB;AAEO,SAAS,iBAAiB,MAAmC;IAClE,MAAM,QAAQ;QACZ,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IACA,OAAO,KAAK,CAAC,OAAO;AACtB;AAEO,SAAS,yBAAyB,MAAc,EAAE,QAAgB;IACvE,iFAAiF;IACjF,MAAM,iBAAiB,QAAQ,oCAAoC;IACnE,OAAO,SAAS,WAAW;AAC7B;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,MAAM,CAAC,GAAG,aAAa;AACrC;AAEO,SAAS,mBAAmB,UAAkB;IAMnD,MAAM,MAAM,IAAI,OAAO,OAAO;IAC9B,MAAM,SAAS,IAAI,KAAK,YAAY,OAAO;IAC3C,MAAM,aAAa,SAAS;IAE5B,IAAI,cAAc,GAAG;QACnB,OAAO;YAAE,OAAO;YAAG,SAAS;YAAG,SAAS;YAAG,SAAS;QAAK;IAC3D;IAEA,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa,CAAC,OAAO,KAAK,EAAE;IACrD,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,aAAa,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IACvE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,aAAa,CAAC,OAAO,EAAE,IAAK;IAExD,OAAO;QAAE;QAAO;QAAS;QAAS,SAAS;IAAM;AACnD;AAEO,SAAS,aAAa,GAAW;IACtC,MAAM,SAAiC;QACrC,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;IACb;IAEA,OAAO,MAAM,CAAC,IAAI,WAAW,GAAG,IAAI;AACtC;AAEO,SAAS,gBAAgB,cAA0D;IACxF,kDAAkD;IAClD,sDAAsD;IAEtD,IAAI,eAAe,MAAM,KAAK,GAAG,OAAO;IAExC,MAAM,iBAAiB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,GAAG,IAAI,MAAM;IAC1E,MAAM,eAAe,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,GAAG,KAAK,MAAM;IACzE,MAAM,eAAe,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,mBAAmB,MAAM;IAEpF,IAAI,iBAAiB,gBAAgB,eAAe,GAAG,OAAO;IAC9D,IAAI,eAAe,kBAAkB,eAAe,GAAG,OAAO;IAC9D,IAAI,eAAe,GAAG,OAAO;IAE7B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\";\n    \n    const variants = {\n      default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n      destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n      outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n      secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n      ghost: \"hover:bg-accent hover:text-accent-foreground\",\n      link: \"text-primary underline-offset-4 hover:underline\",\n    };\n\n    const sizes = {\n      default: \"h-10 px-4 py-2\",\n      sm: \"h-9 rounded-md px-3\",\n      lg: \"h-11 rounded-md px-8\",\n      icon: \"h-10 w-10\",\n    };\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = \"Button\";\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,aAAa;QACb,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'success';\n}\n\nfunction Badge({ className, variant = 'default', ...props }: BadgeProps) {\n  const variants = {\n    default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n    secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n    destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n    outline: \"text-foreground\",\n    success: \"border-transparent bg-green-500 text-white hover:bg-green-500/80\",\n  };\n\n  return (\n    <div\n      className={cn(\n        \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n        variants[variant],\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Badge };\n"], "names": [], "mappings": ";;;;AACA;;;AAOA,SAAS,MAAM,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAmB;IACrE,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,aAAa;QACb,SAAS;QACT,SAAS;IACX;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0KACA,QAAQ,CAAC,QAAQ,EACjB;QAED,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/store/cart.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist, createJSONStorage } from 'zustand/middleware';\n\nexport interface Product {\n  id: string;\n  name: string;\n  price: number;\n  images: string[];\n  colors: Array<{\n    name: string;\n    value: string;\n    image: string;\n  }>;\n  sizes: string[];\n  description: string;\n  category: string;\n  inStock: boolean;\n  rating: number;\n  reviews: number;\n  ecoRating?: 'A' | 'B' | 'C' | 'D' | 'E';\n  carbonFootprint?: number; // in kg CO2\n}\n\nexport interface CartItem {\n  product: Product;\n  selectedColor: string;\n  selectedSize: string;\n  quantity: number;\n}\n\ninterface CartStore {\n  items: CartItem[];\n  isOpen: boolean;\n  addItem: (product: Product, color: string, size: string, quantity?: number) => void;\n  removeItem: (productId: string, color: string, size: string) => void;\n  updateQuantity: (productId: string, color: string, size: string, quantity: number) => void;\n  clearCart: () => void;\n  getTotalItems: () => number;\n  getTotalPrice: () => number;\n  setIsOpen: (open: boolean) => void;\n  mergeGuestCart: (guestItems: CartItem[]) => void;\n}\n\nexport const useCartStore = create<CartStore>()(\n  persist(\n    (set, get) => ({\n      items: [],\n      isOpen: false,\n      \n      addItem: (product, color, size, quantity = 1) => {\n        const items = get().items;\n        const existingItemIndex = items.findIndex(\n          item => \n            item.product.id === product.id && \n            item.selectedColor === color && \n            item.selectedSize === size\n        );\n\n        if (existingItemIndex > -1) {\n          const updatedItems = [...items];\n          updatedItems[existingItemIndex].quantity += quantity;\n          set({ items: updatedItems });\n        } else {\n          set({ \n            items: [...items, { \n              product, \n              selectedColor: color, \n              selectedSize: size, \n              quantity \n            }] \n          });\n        }\n      },\n\n      removeItem: (productId, color, size) => {\n        set({\n          items: get().items.filter(\n            item => !(\n              item.product.id === productId && \n              item.selectedColor === color && \n              item.selectedSize === size\n            )\n          )\n        });\n      },\n\n      updateQuantity: (productId, color, size, quantity) => {\n        if (quantity <= 0) {\n          get().removeItem(productId, color, size);\n          return;\n        }\n\n        const items = get().items;\n        const updatedItems = items.map(item => {\n          if (\n            item.product.id === productId && \n            item.selectedColor === color && \n            item.selectedSize === size\n          ) {\n            return { ...item, quantity };\n          }\n          return item;\n        });\n        set({ items: updatedItems });\n      },\n\n      clearCart: () => set({ items: [] }),\n\n      getTotalItems: () => {\n        return get().items.reduce((total, item) => total + item.quantity, 0);\n      },\n\n      getTotalPrice: () => {\n        return get().items.reduce(\n          (total, item) => total + (item.product.price * item.quantity), \n          0\n        );\n      },\n\n      setIsOpen: (open) => set({ isOpen: open }),\n\n      mergeGuestCart: (guestItems) => {\n        const currentItems = get().items;\n        const mergedItems = [...currentItems];\n\n        guestItems.forEach(guestItem => {\n          const existingIndex = mergedItems.findIndex(\n            item => \n              item.product.id === guestItem.product.id && \n              item.selectedColor === guestItem.selectedColor && \n              item.selectedSize === guestItem.selectedSize\n          );\n\n          if (existingIndex > -1) {\n            mergedItems[existingIndex].quantity += guestItem.quantity;\n          } else {\n            mergedItems.push(guestItem);\n          }\n        });\n\n        set({ items: mergedItems });\n      },\n    }),\n    {\n      name: 'cart-storage',\n      storage: createJSONStorage(() => localStorage),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA0CO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,OAAO,EAAE;QACT,QAAQ;QAER,SAAS,CAAC,SAAS,OAAO,MAAM,WAAW,CAAC;YAC1C,MAAM,QAAQ,MAAM,KAAK;YACzB,MAAM,oBAAoB,MAAM,SAAS,CACvC,CAAA,OACE,KAAK,OAAO,CAAC,EAAE,KAAK,QAAQ,EAAE,IAC9B,KAAK,aAAa,KAAK,SACvB,KAAK,YAAY,KAAK;YAG1B,IAAI,oBAAoB,CAAC,GAAG;gBAC1B,MAAM,eAAe;uBAAI;iBAAM;gBAC/B,YAAY,CAAC,kBAAkB,CAAC,QAAQ,IAAI;gBAC5C,IAAI;oBAAE,OAAO;gBAAa;YAC5B,OAAO;gBACL,IAAI;oBACF,OAAO;2BAAI;wBAAO;4BAChB;4BACA,eAAe;4BACf,cAAc;4BACd;wBACF;qBAAE;gBACJ;YACF;QACF;QAEA,YAAY,CAAC,WAAW,OAAO;YAC7B,IAAI;gBACF,OAAO,MAAM,KAAK,CAAC,MAAM,CACvB,CAAA,OAAQ,CAAC,CACP,KAAK,OAAO,CAAC,EAAE,KAAK,aACpB,KAAK,aAAa,KAAK,SACvB,KAAK,YAAY,KAAK,IACxB;YAEJ;QACF;QAEA,gBAAgB,CAAC,WAAW,OAAO,MAAM;YACvC,IAAI,YAAY,GAAG;gBACjB,MAAM,UAAU,CAAC,WAAW,OAAO;gBACnC;YACF;YAEA,MAAM,QAAQ,MAAM,KAAK;YACzB,MAAM,eAAe,MAAM,GAAG,CAAC,CAAA;gBAC7B,IACE,KAAK,OAAO,CAAC,EAAE,KAAK,aACpB,KAAK,aAAa,KAAK,SACvB,KAAK,YAAY,KAAK,MACtB;oBACA,OAAO;wBAAE,GAAG,IAAI;wBAAE;oBAAS;gBAC7B;gBACA,OAAO;YACT;YACA,IAAI;gBAAE,OAAO;YAAa;QAC5B;QAEA,WAAW,IAAM,IAAI;gBAAE,OAAO,EAAE;YAAC;QAEjC,eAAe;YACb,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;QACpE;QAEA,eAAe;YACb,OAAO,MAAM,KAAK,CAAC,MAAM,CACvB,CAAC,OAAO,OAAS,QAAS,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAC5D;QAEJ;QAEA,WAAW,CAAC,OAAS,IAAI;gBAAE,QAAQ;YAAK;QAExC,gBAAgB,CAAC;YACf,MAAM,eAAe,MAAM,KAAK;YAChC,MAAM,cAAc;mBAAI;aAAa;YAErC,WAAW,OAAO,CAAC,CAAA;gBACjB,MAAM,gBAAgB,YAAY,SAAS,CACzC,CAAA,OACE,KAAK,OAAO,CAAC,EAAE,KAAK,UAAU,OAAO,CAAC,EAAE,IACxC,KAAK,aAAa,KAAK,UAAU,aAAa,IAC9C,KAAK,YAAY,KAAK,UAAU,YAAY;gBAGhD,IAAI,gBAAgB,CAAC,GAAG;oBACtB,WAAW,CAAC,cAAc,CAAC,QAAQ,IAAI,UAAU,QAAQ;gBAC3D,OAAO;oBACL,YAAY,IAAI,CAAC;gBACnB;YACF;YAEA,IAAI;gBAAE,OAAO;YAAY;QAC3B;IACF,CAAC,GACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,gJAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;AACnC", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/cart/cart-button.tsx"], "sourcesContent": ["'use client';\n\nimport { ShoppingBag } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { useCartStore } from '@/store/cart';\nimport { cn } from '@/lib/utils';\n\ninterface CartButtonProps {\n  className?: string;\n}\n\nexport function CartButton({ className }: CartButtonProps) {\n  const { getTotalItems, setIsOpen } = useCartStore();\n  const itemCount = getTotalItems();\n\n  return (\n    <Button\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"relative\", className)}\n      onClick={() => setIsOpen(true)}\n    >\n      <ShoppingBag className=\"w-6 h-6\" />\n      {itemCount > 0 && (\n        <Badge \n          className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs bg-primary text-primary-foreground\"\n        >\n          {itemCount > 99 ? '99+' : itemCount}\n        </Badge>\n      )}\n    </Button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,WAAW,EAAE,SAAS,EAAmB;;IACvD,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;IAChD,MAAM,YAAY;IAElB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,SAAS,IAAM,UAAU;;0BAEzB,6LAAC,uNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YACtB,YAAY,mBACX,6LAAC,oIAAA,CAAA,QAAK;gBACJ,WAAU;0BAET,YAAY,KAAK,QAAQ;;;;;;;;;;;;AAKpC;GArBgB;;QACuB,uHAAA,CAAA,eAAY;;;KADnC", "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/layout/header.tsx"], "sourcesContent": ["'use client';\n\nimport { Search, User, Heart, Menu } from 'lucide-react';\nimport { <PERSON><PERSON> } from '@/components/ui/button';\nimport { CartButton } from '@/components/cart/cart-button';\n\nexport function Header() {\n  return (\n    <header className=\"sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Mobile Menu */}\n          <Button variant=\"ghost\" size=\"icon\" className=\"md:hidden\">\n            <Menu className=\"w-5 h-5\" />\n          </Button>\n\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <h1 className=\"text-xl font-bold\">NextGen Store</h1>\n          </div>\n\n          {/* Navigation - Desktop */}\n          <nav className=\"hidden md:flex items-center space-x-6\">\n            <a href=\"#\" className=\"text-sm font-medium hover:text-primary transition-colors\">\n              New Arrivals\n            </a>\n            <a href=\"#\" className=\"text-sm font-medium hover:text-primary transition-colors\">\n              Clothing\n            </a>\n            <a href=\"#\" className=\"text-sm font-medium hover:text-primary transition-colors\">\n              Electronics\n            </a>\n            <a href=\"#\" className=\"text-sm font-medium hover:text-primary transition-colors\">\n              Accessories\n            </a>\n            <a href=\"#\" className=\"text-sm font-medium hover:text-primary transition-colors\">\n              Sustainable\n            </a>\n          </nav>\n\n          {/* Search Bar - Desktop */}\n          <div className=\"hidden md:flex items-center flex-1 max-w-md mx-6\">\n            <div className=\"relative w-full\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search products...\"\n                className=\"w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent\"\n              />\n            </div>\n          </div>\n\n          {/* Actions */}\n          <div className=\"flex items-center space-x-2\">\n            {/* Search - Mobile */}\n            <Button variant=\"ghost\" size=\"icon\" className=\"md:hidden\">\n              <Search className=\"w-5 h-5\" />\n            </Button>\n\n            {/* Wishlist */}\n            <Button variant=\"ghost\" size=\"icon\">\n              <Heart className=\"w-5 h-5\" />\n            </Button>\n\n            {/* Account */}\n            <Button variant=\"ghost\" size=\"icon\">\n              <User className=\"w-5 h-5\" />\n            </Button>\n\n            {/* Cart */}\n            <CartButton />\n          </div>\n        </div>\n\n        {/* Mobile Search */}\n        <div className=\"md:hidden pb-4\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search products...\"\n              className=\"w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent\"\n            />\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AAJA;;;;;AAMO,SAAS;IACd,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAO,WAAU;sCAC5C,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAIlB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CAAoB;;;;;;;;;;;sCAIpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,MAAK;oCAAI,WAAU;8CAA2D;;;;;;8CAGjF,6LAAC;oCAAE,MAAK;oCAAI,WAAU;8CAA2D;;;;;;8CAGjF,6LAAC;oCAAE,MAAK;oCAAI,WAAU;8CAA2D;;;;;;8CAGjF,6LAAC;oCAAE,MAAK;oCAAI,WAAU;8CAA2D;;;;;;8CAGjF,6LAAC;oCAAE,MAAK;oCAAI,WAAU;8CAA2D;;;;;;;;;;;;sCAMnF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,WAAU;8CAC5C,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAIpB,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAInB,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAIlB,6LAAC,+IAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;8BAKf,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB;KAlFgB", "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/cart/cart-sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { X, Plus, Minus, Trash2 } from 'lucide-react';\nimport Image from 'next/image';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { useCartStore } from '@/store/cart';\nimport { formatPrice } from '@/lib/utils';\n\n\nexport function CartSidebar() {\n  const {\n    items,\n    isOpen,\n    setIsOpen,\n    updateQuantity,\n    removeItem,\n    getTotalPrice,\n    clearCart\n  } = useCartStore();\n\n  if (!isOpen) return null;\n\n  return (\n    <>\n      {/* Backdrop */}\n      <div\n        className=\"fixed inset-0 bg-black/50 z-40\"\n        onClick={() => setIsOpen(false)}\n      />\n\n      {/* Sidebar */}\n      <div className=\"fixed right-0 top-0 h-full w-full max-w-md bg-white z-50 shadow-xl flex flex-col\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 border-b\">\n          <h2 className=\"text-lg font-semibold\">Shopping Cart</h2>\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={() => setIsOpen(false)}\n          >\n            <X className=\"w-5 h-5\" />\n          </Button>\n        </div>\n\n        {/* Cart Items */}\n        <div className=\"flex-1 overflow-y-auto p-4\">\n          {items.length === 0 ? (\n            <div className=\"flex flex-col items-center justify-center h-full text-center\">\n              <div className=\"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4\">\n                <svg className=\"w-12 h-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-medium mb-2\">Your cart is empty</h3>\n              <p className=\"text-gray-500 mb-4\">Add some items to get started</p>\n              <Button onClick={() => setIsOpen(false)}>\n                Continue Shopping\n              </Button>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {items.map((item) => (\n                <div key={`${item.product.id}-${item.selectedColor}-${item.selectedSize}`} className=\"flex gap-3 p-3 border rounded-lg\">\n                  {/* Product Image */}\n                  <div className=\"relative w-16 h-16 flex-shrink-0\">\n                    <Image\n                      src={item.product.images[0]}\n                      alt={item.product.name}\n                      fill\n                      className=\"object-cover rounded\"\n                    />\n                  </div>\n\n                  {/* Product Details */}\n                  <div className=\"flex-1 min-w-0\">\n                    <h4 className=\"font-medium text-sm truncate\">\n                      {item.product.name}\n                    </h4>\n                    <div className=\"flex items-center gap-2 mt-1\">\n                      <div\n                        className=\"w-3 h-3 rounded-full border\"\n                        style={{ backgroundColor: item.product.colors.find(c => c.name === item.selectedColor)?.value }}\n                      />\n                      <span className=\"text-xs text-gray-500\">\n                        {item.selectedColor}\n                      </span>\n                      <Badge variant=\"outline\" className=\"text-xs\">\n                        {item.selectedSize}\n                      </Badge>\n                    </div>\n                    <div className=\"flex items-center justify-between mt-2\">\n                      <span className=\"font-semibold text-sm\">\n                        {formatPrice(item.product.price)}\n                      </span>\n\n                      {/* Quantity Controls */}\n                      <div className=\"flex items-center gap-1\">\n                        <Button\n                          variant=\"outline\"\n                          size=\"icon\"\n                          className=\"w-6 h-6\"\n                          onClick={() => updateQuantity(\n                            item.product.id,\n                            item.selectedColor,\n                            item.selectedSize,\n                            item.quantity - 1\n                          )}\n                        >\n                          <Minus className=\"w-3 h-3\" />\n                        </Button>\n                        <span className=\"w-8 text-center text-sm\">\n                          {item.quantity}\n                        </span>\n                        <Button\n                          variant=\"outline\"\n                          size=\"icon\"\n                          className=\"w-6 h-6\"\n                          onClick={() => updateQuantity(\n                            item.product.id,\n                            item.selectedColor,\n                            item.selectedSize,\n                            item.quantity + 1\n                          )}\n                        >\n                          <Plus className=\"w-3 h-3\" />\n                        </Button>\n                        <Button\n                          variant=\"ghost\"\n                          size=\"icon\"\n                          className=\"w-6 h-6 text-red-500 hover:text-red-700\"\n                          onClick={() => removeItem(\n                            item.product.id,\n                            item.selectedColor,\n                            item.selectedSize\n                          )}\n                        >\n                          <Trash2 className=\"w-3 h-3\" />\n                        </Button>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* Footer */}\n        {items.length > 0 && (\n          <div className=\"border-t p-4 space-y-4\">\n            {/* Clear Cart */}\n            <Button\n              variant=\"outline\"\n              className=\"w-full\"\n              onClick={clearCart}\n            >\n              Clear Cart\n            </Button>\n\n            {/* Total */}\n            <div className=\"flex items-center justify-between text-lg font-semibold\">\n              <span>Total:</span>\n              <span>{formatPrice(getTotalPrice())}</span>\n            </div>\n\n            {/* Checkout Button */}\n            <Button className=\"w-full\" size=\"lg\">\n              Checkout\n            </Button>\n          </div>\n        )}\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAUO,SAAS;;IACd,MAAM,EACJ,KAAK,EACL,MAAM,EACN,SAAS,EACT,cAAc,EACd,UAAU,EACV,aAAa,EACb,SAAS,EACV,GAAG,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;IAEf,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE;;0BAEE,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;0BAI3B,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,UAAU;0CAEzB,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKjB,6LAAC;wBAAI,WAAU;kCACZ,MAAM,MAAM,KAAK,kBAChB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAA0B,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjF,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAG,WAAU;8CAA2B;;;;;;8CACzC,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,UAAU;8CAAQ;;;;;;;;;;;iDAK3C,6LAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;oCAA0E,WAAU;;sDAEnF,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,OAAO,CAAC,MAAM,CAAC,EAAE;gDAC3B,KAAK,KAAK,OAAO,CAAC,IAAI;gDACtB,IAAI;gDACJ,WAAU;;;;;;;;;;;sDAKd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,KAAK,OAAO,CAAC,IAAI;;;;;;8DAEpB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,iBAAiB,KAAK,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,KAAK,aAAa,GAAG;4DAAM;;;;;;sEAEhG,6LAAC;4DAAK,WAAU;sEACb,KAAK,aAAa;;;;;;sEAErB,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAChC,KAAK,YAAY;;;;;;;;;;;;8DAGtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,OAAO,CAAC,KAAK;;;;;;sEAIjC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,eACb,KAAK,OAAO,CAAC,EAAE,EACf,KAAK,aAAa,EAClB,KAAK,YAAY,EACjB,KAAK,QAAQ,GAAG;8EAGlB,cAAA,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;8EAEnB,6LAAC;oEAAK,WAAU;8EACb,KAAK,QAAQ;;;;;;8EAEhB,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,eACb,KAAK,OAAO,CAAC,EAAE,EACf,KAAK,aAAa,EAClB,KAAK,YAAY,EACjB,KAAK,QAAQ,GAAG;8EAGlB,cAAA,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,WACb,KAAK,OAAO,CAAC,EAAE,EACf,KAAK,aAAa,EAClB,KAAK,YAAY;8EAGnB,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCA1ElB,GAAG,KAAK,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,aAAa,CAAC,CAAC,EAAE,KAAK,YAAY,EAAE;;;;;;;;;;;;;;;oBAsFhF,MAAM,MAAM,GAAG,mBACd,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;0CACV;;;;;;0CAKD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC;kDAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;0CAIrB,6LAAC,qIAAA,CAAA,SAAM;gCAAC,WAAU;gCAAS,MAAK;0CAAK;;;;;;;;;;;;;;;;;;;;AAQjD;GArKgB;;QASV,uHAAA,CAAA,eAAY;;;KATF", "debugId": null}}]}