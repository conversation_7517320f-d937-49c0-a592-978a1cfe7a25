import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { Header } from "@/components/layout/header";
import { CartSidebar } from "@/components/cart/cart-sidebar";
import { AIChatbot } from "@/components/ai/ai-chatbot";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "NextGen Store - Smart eCommerce Experience",
  description: "A next-generation eCommerce platform with AI-powered features, sustainable shopping, and personalized experiences.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} min-h-screen bg-background font-sans antialiased`}
      >
        <Header />
        <main className="flex-1">
          {children}
        </main>
        <CartSidebar />
        <AIChatbot />
      </body>
    </html>
  );
}
