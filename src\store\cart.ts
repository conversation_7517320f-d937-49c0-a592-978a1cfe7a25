import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

export interface Product {
  id: string;
  name: string;
  price: number;
  images: string[];
  colors: Array<{
    name: string;
    value: string;
    image: string;
  }>;
  sizes: string[];
  description: string;
  category: string;
  inStock: boolean;
  rating: number;
  reviews: number;
  ecoRating?: 'A' | 'B' | 'C' | 'D' | 'E';
  carbonFootprint?: number; // in kg CO2
}

export interface CartItem {
  product: Product;
  selectedColor: string;
  selectedSize: string;
  quantity: number;
}

interface CartStore {
  items: CartItem[];
  isOpen: boolean;
  addItem: (product: Product, color: string, size: string, quantity?: number) => void;
  removeItem: (productId: string, color: string, size: string) => void;
  updateQuantity: (productId: string, color: string, size: string, quantity: number) => void;
  clearCart: () => void;
  getTotalItems: () => number;
  getTotalPrice: () => number;
  setIsOpen: (open: boolean) => void;
  mergeGuestCart: (guestItems: CartItem[]) => void;
}

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      isOpen: false,
      
      addItem: (product, color, size, quantity = 1) => {
        const items = get().items;
        const existingItemIndex = items.findIndex(
          item => 
            item.product.id === product.id && 
            item.selectedColor === color && 
            item.selectedSize === size
        );

        if (existingItemIndex > -1) {
          const updatedItems = [...items];
          updatedItems[existingItemIndex].quantity += quantity;
          set({ items: updatedItems });
        } else {
          set({ 
            items: [...items, { 
              product, 
              selectedColor: color, 
              selectedSize: size, 
              quantity 
            }] 
          });
        }
      },

      removeItem: (productId, color, size) => {
        set({
          items: get().items.filter(
            item => !(
              item.product.id === productId && 
              item.selectedColor === color && 
              item.selectedSize === size
            )
          )
        });
      },

      updateQuantity: (productId, color, size, quantity) => {
        if (quantity <= 0) {
          get().removeItem(productId, color, size);
          return;
        }

        const items = get().items;
        const updatedItems = items.map(item => {
          if (
            item.product.id === productId && 
            item.selectedColor === color && 
            item.selectedSize === size
          ) {
            return { ...item, quantity };
          }
          return item;
        });
        set({ items: updatedItems });
      },

      clearCart: () => set({ items: [] }),

      getTotalItems: () => {
        return get().items.reduce((total, item) => total + item.quantity, 0);
      },

      getTotalPrice: () => {
        return get().items.reduce(
          (total, item) => total + (item.product.price * item.quantity), 
          0
        );
      },

      setIsOpen: (open) => set({ isOpen: open }),

      mergeGuestCart: (guestItems) => {
        const currentItems = get().items;
        const mergedItems = [...currentItems];

        guestItems.forEach(guestItem => {
          const existingIndex = mergedItems.findIndex(
            item => 
              item.product.id === guestItem.product.id && 
              item.selectedColor === guestItem.selectedColor && 
              item.selectedSize === guestItem.selectedSize
          );

          if (existingIndex > -1) {
            mergedItems[existingIndex].quantity += guestItem.quantity;
          } else {
            mergedItems.push(guestItem);
          }
        });

        set({ items: mergedItems });
      },
    }),
    {
      name: 'cart-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
