'use client';

import { useState } from 'react';
import { X, Star, Leaf, AlertCircle } from 'lucide-react';
import Image from 'next/image';
import { Product } from '@/types';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useCartStore } from '@/store/cart';
import { formatPrice, getEcoRatingColor, cn } from '@/lib/utils';

interface ProductSelectionModalProps {
  product: Product | null;
  isOpen: boolean;
  onClose: () => void;
}

export function ProductSelectionModal({ product, isOpen, onClose }: ProductSelectionModalProps) {
  const [selectedColor, setSelectedColor] = useState<string>('');
  const [selectedSize, setSelectedSize] = useState<string>('');
  const [currentImage, setCurrentImage] = useState<string>('');
  const [quantity, setQuantity] = useState(1);
  const [showValidation, setShowValidation] = useState(false);

  const { addItem } = useCartStore();

  if (!isOpen || !product) return null;

  // Reset state when product changes
  if (currentImage === '' && product.images.length > 0) {
    setCurrentImage(product.images[0]);
  }

  const handleColorSelect = (colorName: string, colorImage: string) => {
    setSelectedColor(colorName);
    setCurrentImage(colorImage);
    setShowValidation(false);
  };

  const handleSizeSelect = (size: string) => {
    setSelectedSize(size);
    setShowValidation(false);
  };

  const handleAddToCart = () => {
    if (!selectedColor || !selectedSize) {
      setShowValidation(true);
      return;
    }

    addItem(product, selectedColor, selectedSize, quantity);

    // Reset form
    setSelectedColor('');
    setSelectedSize('');
    setQuantity(1);
    setShowValidation(false);
    onClose();
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          "w-4 h-4",
          i < Math.floor(rating)
            ? "fill-yellow-400 text-yellow-400"
            : "text-gray-300"
        )}
      />
    ));
  };

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 z-50"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b">
            <h2 className="text-xl font-semibold">Select Options</h2>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="w-5 h-5" />
            </Button>
          </div>

          <div className="p-6">
            <div className="grid md:grid-cols-2 gap-6">
              {/* Product Image */}
              <div className="relative aspect-square">
                <Image
                  src={currentImage}
                  alt={product.name}
                  fill
                  className="object-cover rounded-lg"
                />
                {product.ecoRating && (
                  <Badge
                    className={cn(
                      "absolute top-2 left-2 text-white",
                      getEcoRatingColor(product.ecoRating)
                    )}
                  >
                    <Leaf className="w-3 h-3 mr-1" />
                    {product.ecoRating}
                  </Badge>
                )}
              </div>

              {/* Product Details */}
              <div className="space-y-4">
                {/* Brand & Name */}
                {product.brand && (
                  <p className="text-sm text-muted-foreground uppercase tracking-wide">
                    {product.brand}
                  </p>
                )}
                <h3 className="text-2xl font-bold">{product.name}</h3>

                {/* Rating */}
                <div className="flex items-center gap-2">
                  <div className="flex">
                    {renderStars(product.rating)}
                  </div>
                  <span className="text-sm text-muted-foreground">
                    ({product.reviews} reviews)
                  </span>
                </div>

                {/* Price */}
                <div className="text-3xl font-bold">
                  {formatPrice(product.price)}
                </div>

                {/* Validation Message */}
                {showValidation && (!selectedColor || !selectedSize) && (
                  <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <AlertCircle className="w-4 h-4 text-red-500" />
                    <span className="text-sm text-red-700">
                      Please select color and size to add product into cart.
                    </span>
                  </div>
                )}

                {/* Color Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Color {!selectedColor && showValidation && <span className="text-red-500">*</span>}
                  </label>
                  <div className="flex gap-2 flex-wrap">
                    {product.colors.map((color, index) => (
                      <button
                        key={index}
                        type="button"
                        className={cn(
                          "flex items-center gap-2 px-3 py-2 border rounded-lg transition-all",
                          selectedColor === color.name
                            ? "border-primary bg-primary/10"
                            : "border-gray-300 hover:border-gray-400"
                        )}
                        onClick={() => handleColorSelect(color.name, color.image)}
                      >
                        <div
                          className="w-4 h-4 rounded-full border"
                          style={{ backgroundColor: color.value }}
                        />
                        <span className="text-sm">{color.name}</span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Size Selection */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Size {!selectedSize && showValidation && <span className="text-red-500">*</span>}
                  </label>
                  <div className="flex gap-2 flex-wrap">
                    {product.sizes.map((size, index) => (
                      <button
                        key={index}
                        type="button"
                        className={cn(
                          "px-3 py-2 border rounded-lg transition-all text-sm",
                          selectedSize === size
                            ? "border-primary bg-primary/10"
                            : "border-gray-300 hover:border-gray-400"
                        )}
                        onClick={() => handleSizeSelect(size)}
                      >
                        {size}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Quantity */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Quantity</label>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    >
                      -
                    </Button>
                    <span className="w-12 text-center">{quantity}</span>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setQuantity(quantity + 1)}
                    >
                      +
                    </Button>
                  </div>
                </div>

                {/* Add to Cart Button */}
                <Button
                  className="w-full"
                  size="lg"
                  onClick={handleAddToCart}
                >
                  Add to Cart - {formatPrice(product.price * quantity)}
                </Button>

                {/* Product Description */}
                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-2">Description</h4>
                  <p className="text-sm text-muted-foreground">
                    {product.description}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
