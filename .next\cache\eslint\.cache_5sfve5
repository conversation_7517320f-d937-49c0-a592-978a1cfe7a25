[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\cart\\cart-button.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\cart\\cart-sidebar.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\layout\\header.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\product\\product-card.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\product\\product-grid.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\product\\product-selection-modal.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\ui\\badge.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\ui\\button.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\ui\\card.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\data\\products.ts": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\lib\\utils.ts": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\store\\cart.ts": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\types\\index.ts": "15"}, {"size": 1049, "mtime": 1748354768084, "results": "16", "hashOfConfig": "17"}, {"size": 3982, "mtime": 1748356374634, "results": "18", "hashOfConfig": "17"}, {"size": 916, "mtime": 1748354634624, "results": "19", "hashOfConfig": "17"}, {"size": 6465, "mtime": 1748355767874, "results": "20", "hashOfConfig": "17"}, {"size": 3397, "mtime": 1748354711406, "results": "21", "hashOfConfig": "17"}, {"size": 5461, "mtime": 1748355778534, "results": "22", "hashOfConfig": "17"}, {"size": 1287, "mtime": 1748354721533, "results": "23", "hashOfConfig": "17"}, {"size": 8789, "mtime": 1748355638591, "results": "24", "hashOfConfig": "17"}, {"size": 1056, "mtime": 1748354587654, "results": "25", "hashOfConfig": "17"}, {"size": 1652, "mtime": 1748354580101, "results": "26", "hashOfConfig": "17"}, {"size": 1891, "mtime": 1748354599136, "results": "27", "hashOfConfig": "17"}, {"size": 6523, "mtime": 1748354566968, "results": "28", "hashOfConfig": "17"}, {"size": 3730, "mtime": 1748355839224, "results": "29", "hashOfConfig": "17"}, {"size": 3981, "mtime": 1748354487711, "results": "30", "hashOfConfig": "17"}, {"size": 2329, "mtime": 1748354507033, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "jtq9ed", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\cart\\cart-button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\cart\\cart-sidebar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\layout\\header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\product\\product-card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\product\\product-grid.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\product\\product-selection-modal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\data\\products.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\store\\cart.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\types\\index.ts", [], []]