[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\cart\\cart-button.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\cart\\cart-sidebar.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\layout\\header.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\product\\product-card.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\product\\product-grid.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\product\\product-selection-modal.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\ui\\badge.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\ui\\button.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\ui\\card.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\data\\products.ts": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\lib\\utils.ts": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\store\\cart.ts": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\types\\index.ts": "15"}, {"size": 1049, "mtime": 1748354768084, "results": "16", "hashOfConfig": "17"}, {"size": 3972, "mtime": 1748355597375, "results": "18", "hashOfConfig": "17"}, {"size": 916, "mtime": 1748354634624, "results": "19", "hashOfConfig": "17"}, {"size": 6550, "mtime": 1748354658655, "results": "20", "hashOfConfig": "17"}, {"size": 3397, "mtime": 1748354711406, "results": "21", "hashOfConfig": "17"}, {"size": 5479, "mtime": 1748355617910, "results": "22", "hashOfConfig": "17"}, {"size": 1287, "mtime": 1748354721533, "results": "23", "hashOfConfig": "17"}, {"size": 8789, "mtime": 1748355638591, "results": "24", "hashOfConfig": "17"}, {"size": 1056, "mtime": 1748354587654, "results": "25", "hashOfConfig": "17"}, {"size": 1652, "mtime": 1748354580101, "results": "26", "hashOfConfig": "17"}, {"size": 1891, "mtime": 1748354599136, "results": "27", "hashOfConfig": "17"}, {"size": 6523, "mtime": 1748354566968, "results": "28", "hashOfConfig": "17"}, {"size": 3693, "mtime": 1748354528474, "results": "29", "hashOfConfig": "17"}, {"size": 3981, "mtime": 1748354487711, "results": "30", "hashOfConfig": "17"}, {"size": 2329, "mtime": 1748354507033, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "jtq9ed", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\app\\page.tsx", ["77", "78"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\cart\\cart-button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\cart\\cart-sidebar.tsx", ["79", "80"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\layout\\header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\product\\product-card.tsx", ["81"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\product\\product-grid.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\product\\product-selection-modal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\data\\products.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\lib\\utils.ts", ["82", "83", "84"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\store\\cart.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\types\\index.ts", [], [], {"ruleId": "85", "severity": 2, "message": "86", "line": 53, "column": 15, "nodeType": "87", "messageId": "88", "suggestions": "89"}, {"ruleId": "85", "severity": 2, "message": "86", "line": 53, "column": 52, "nodeType": "87", "messageId": "88", "suggestions": "90"}, {"ruleId": "91", "severity": 2, "message": "92", "line": 9, "column": 10, "nodeType": null, "messageId": "93", "endLine": 9, "endColumn": 12}, {"ruleId": "91", "severity": 2, "message": "94", "line": 63, "column": 33, "nodeType": null, "messageId": "93", "endLine": 63, "endColumn": 38}, {"ruleId": "91", "severity": 2, "message": "95", "line": 10, "column": 42, "nodeType": null, "messageId": "93", "endLine": 10, "endColumn": 58}, {"ruleId": "96", "severity": 2, "message": "97", "line": 46, "column": 46, "nodeType": "98", "messageId": "99", "endLine": 46, "endColumn": 49, "suggestions": "100"}, {"ruleId": "96", "severity": 2, "message": "97", "line": 46, "column": 56, "nodeType": "98", "messageId": "99", "endLine": 46, "endColumn": 59, "suggestions": "101"}, {"ruleId": "96", "severity": 2, "message": "97", "line": 115, "column": 49, "nodeType": "98", "messageId": "99", "endLine": 115, "endColumn": 52, "suggestions": "102"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["103", "104", "105", "106"], ["107", "108", "109", "110"], "@typescript-eslint/no-unused-vars", "'cn' is defined but never used.", "unusedVar", "'index' is defined but never used.", "'getEcoRatingText' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["111", "112"], ["113", "114"], ["115", "116"], {"messageId": "117", "data": "118", "fix": "119", "desc": "120"}, {"messageId": "117", "data": "121", "fix": "122", "desc": "123"}, {"messageId": "117", "data": "124", "fix": "125", "desc": "126"}, {"messageId": "117", "data": "127", "fix": "128", "desc": "129"}, {"messageId": "117", "data": "130", "fix": "131", "desc": "120"}, {"messageId": "117", "data": "132", "fix": "133", "desc": "123"}, {"messageId": "117", "data": "134", "fix": "135", "desc": "126"}, {"messageId": "117", "data": "136", "fix": "137", "desc": "129"}, {"messageId": "138", "fix": "139", "desc": "140"}, {"messageId": "141", "fix": "142", "desc": "143"}, {"messageId": "138", "fix": "144", "desc": "140"}, {"messageId": "141", "fix": "145", "desc": "143"}, {"messageId": "138", "fix": "146", "desc": "140"}, {"messageId": "141", "fix": "147", "desc": "143"}, "replaceWithAlt", {"alt": "148"}, {"range": "149", "text": "150"}, "Replace with `&apos;`.", {"alt": "151"}, {"range": "152", "text": "153"}, "Replace with `&lsquo;`.", {"alt": "154"}, {"range": "155", "text": "156"}, "Replace with `&#39;`.", {"alt": "157"}, {"range": "158", "text": "159"}, "Replace with `&rsquo;`.", {"alt": "148"}, {"range": "160", "text": "161"}, {"alt": "151"}, {"range": "162", "text": "163"}, {"alt": "154"}, {"range": "164", "text": "165"}, {"alt": "157"}, {"range": "166", "text": "167"}, "suggestUnknown", {"range": "168", "text": "169"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "170", "text": "171"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "172", "text": "169"}, {"range": "173", "text": "171"}, {"range": "174", "text": "169"}, {"range": "175", "text": "171"}, "&apos;", [2094, 2263], "\n            We&apos;re not just another online store. We're pioneering the future of eCommerce\n            with cutting-edge technology and sustainable practices.\n          ", "&lsquo;", [2094, 2263], "\n            We&lsquo;re not just another online store. We're pioneering the future of eCommerce\n            with cutting-edge technology and sustainable practices.\n          ", "&#39;", [2094, 2263], "\n            We&#39;re not just another online store. We're pioneering the future of eCommerce\n            with cutting-edge technology and sustainable practices.\n          ", "&rsquo;", [2094, 2263], "\n            We&rsquo;re not just another online store. We're pioneering the future of eCommerce\n            with cutting-edge technology and sustainable practices.\n          ", [2094, 2263], "\n            We're not just another online store. We&apos;re pioneering the future of eCommerce\n            with cutting-edge technology and sustainable practices.\n          ", [2094, 2263], "\n            We're not just another online store. We&lsquo;re pioneering the future of eCommerce\n            with cutting-edge technology and sustainable practices.\n          ", [2094, 2263], "\n            We're not just another online store. We&#39;re pioneering the future of eCommerce\n            with cutting-edge technology and sustainable practices.\n          ", [2094, 2263], "\n            We're not just another online store. We&rsquo;re pioneering the future of eCommerce\n            with cutting-edge technology and sustainable practices.\n          ", [1161, 1164], "unknown", [1161, 1164], "never", [1171, 1174], [1171, 1174], [2991, 2994], [2991, 2994]]