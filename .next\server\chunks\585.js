exports.id=585,exports.ids=[585],exports.modules={494:(e,t,s)=>{"use strict";s.d(t,{CartSidebar:()=>x});var r=s(687),n=s(1860),i=s(5748),o=s(6474),a=s(8233),l=s(474),c=s(9523),d=s(6834),m=s(5801),u=s(4780);function x(){let{items:e,isOpen:t,setIsOpen:s,updateQuantity:x,removeItem:h,getTotalPrice:f,clearCart:p}=(0,m.x)();return t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 z-40",onClick:()=>s(!1)}),(0,r.jsxs)("div",{className:"fixed right-0 top-0 h-full w-full max-w-md bg-white z-50 shadow-xl flex flex-col",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold",children:"Shopping Cart"}),(0,r.jsx)(c.$,{variant:"ghost",size:"icon",onClick:()=>s(!1),children:(0,r.jsx)(n.A,{className:"w-5 h-5"})})]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:0===e.length?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full text-center",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4",children:(0,r.jsx)("svg",{className:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})})}),(0,r.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Your cart is empty"}),(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"Add some items to get started"}),(0,r.jsx)(c.$,{onClick:()=>s(!1),children:"Continue Shopping"})]}):(0,r.jsx)("div",{className:"space-y-4",children:e.map(e=>(0,r.jsxs)("div",{className:"flex gap-3 p-3 border rounded-lg",children:[(0,r.jsx)("div",{className:"relative w-16 h-16 flex-shrink-0",children:(0,r.jsx)(l.default,{src:e.product.images[0],alt:e.product.name,fill:!0,className:"object-cover rounded"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h4",{className:"font-medium text-sm truncate",children:e.product.name}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full border",style:{backgroundColor:e.product.colors.find(t=>t.name===e.selectedColor)?.value}}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:e.selectedColor}),(0,r.jsx)(d.E,{variant:"outline",className:"text-xs",children:e.selectedSize})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,r.jsx)("span",{className:"font-semibold text-sm",children:(0,u.$g)(e.product.price)}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(c.$,{variant:"outline",size:"icon",className:"w-6 h-6",onClick:()=>x(e.product.id,e.selectedColor,e.selectedSize,e.quantity-1),children:(0,r.jsx)(i.A,{className:"w-3 h-3"})}),(0,r.jsx)("span",{className:"w-8 text-center text-sm",children:e.quantity}),(0,r.jsx)(c.$,{variant:"outline",size:"icon",className:"w-6 h-6",onClick:()=>x(e.product.id,e.selectedColor,e.selectedSize,e.quantity+1),children:(0,r.jsx)(o.A,{className:"w-3 h-3"})}),(0,r.jsx)(c.$,{variant:"ghost",size:"icon",className:"w-6 h-6 text-red-500 hover:text-red-700",onClick:()=>h(e.product.id,e.selectedColor,e.selectedSize),children:(0,r.jsx)(a.A,{className:"w-3 h-3"})})]})]})]})]},`${e.product.id}-${e.selectedColor}-${e.selectedSize}`))})}),e.length>0&&(0,r.jsxs)("div",{className:"border-t p-4 space-y-4",children:[(0,r.jsx)(c.$,{variant:"outline",className:"w-full",onClick:p,children:"Clear Cart"}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-lg font-semibold",children:[(0,r.jsx)("span",{children:"Total:"}),(0,r.jsx)("span",{children:(0,u.$g)(f())})]}),(0,r.jsx)(c.$,{className:"w-full",size:"lg",children:"Checkout"})]})]})]}):null}},590:(e,t,s)=>{"use strict";s.d(t,{Header:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\layout\\header.tsx","Header")},979:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},1135:()=>{},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m,metadata:()=>d});var r=s(7413),n=s(2376),i=s.n(n),o=s(8726),a=s.n(o);s(1135);var l=s(590),c=s(5472);let d={title:"NextGen Store - Smart eCommerce Experience",description:"A next-generation eCommerce platform with AI-powered features, sustainable shopping, and personalized experiences."};function m({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsxs)("body",{className:`${i().variable} ${a().variable} min-h-screen bg-background font-sans antialiased`,children:[(0,r.jsx)(l.Header,{}),(0,r.jsx)("main",{className:"flex-1",children:e}),(0,r.jsx)(c.CartSidebar,{})]})})}},4780:(e,t,s)=>{"use strict";s.d(t,{$g:()=>i,cn:()=>n,sj:()=>o});var r=s(9384);function n(...e){return(0,r.$)(e)}function i(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e)}function o(e){return({A:"bg-green-500",B:"bg-lime-500",C:"bg-yellow-500",D:"bg-orange-500",E:"bg-red-500"})[e]}},5051:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},5472:(e,t,s)=>{"use strict";s.d(t,{CartSidebar:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call CartSidebar() from the server but CartSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\cart\\cart-sidebar.tsx","CartSidebar")},5736:(e,t,s)=>{Promise.resolve().then(s.bind(s,5472)),Promise.resolve().then(s.bind(s,590))},5801:(e,t,s)=>{"use strict";s.d(t,{x:()=>i});var r=s(6787),n=s(9350);let i=(0,r.v)()((0,n.Zr)((e,t)=>({items:[],isOpen:!1,addItem:(s,r,n,i=1)=>{let o=t().items,a=o.findIndex(e=>e.product.id===s.id&&e.selectedColor===r&&e.selectedSize===n);if(a>-1){let t=[...o];t[a].quantity+=i,e({items:t})}else e({items:[...o,{product:s,selectedColor:r,selectedSize:n,quantity:i}]})},removeItem:(s,r,n)=>{e({items:t().items.filter(e=>e.product.id!==s||e.selectedColor!==r||e.selectedSize!==n)})},updateQuantity:(s,r,n,i)=>{if(i<=0)return void t().removeItem(s,r,n);e({items:t().items.map(e=>e.product.id===s&&e.selectedColor===r&&e.selectedSize===n?{...e,quantity:i}:e)})},clearCart:()=>e({items:[]}),getTotalItems:()=>t().items.reduce((e,t)=>e+t.quantity,0),getTotalPrice:()=>t().items.reduce((e,t)=>e+t.product.price*t.quantity,0),setIsOpen:t=>e({isOpen:t}),mergeGuestCart:s=>{let r=[...t().items];s.forEach(e=>{let t=r.findIndex(t=>t.product.id===e.product.id&&t.selectedColor===e.selectedColor&&t.selectedSize===e.selectedSize);t>-1?r[t].quantity+=e.quantity:r.push(e)}),e({items:r})}}),{name:"cart-storage",storage:(0,n.KU)(()=>localStorage)}))},6834:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var r=s(687);s(3210);var n=s(4780);function i({className:e,variant:t="default",...s}){return(0,r.jsx)("div",{className:(0,n.cn)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80"}[t],e),...s})}},8832:(e,t,s)=>{"use strict";s.d(t,{Header:()=>h});var r=s(687),n=s(2941),i=s(9270),o=s(7760),a=s(8869),l=s(9523),c=s(1057),d=s(6834),m=s(5801),u=s(4780);function x({className:e}){let{getTotalItems:t,setIsOpen:s}=(0,m.x)(),n=t();return(0,r.jsxs)(l.$,{variant:"ghost",size:"icon",className:(0,u.cn)("relative",e),onClick:()=>s(!0),children:[(0,r.jsx)(c.A,{className:"w-6 h-6"}),n>0&&(0,r.jsx)(d.E,{className:"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs bg-primary text-primary-foreground",children:n>99?"99+":n})]})}function h(){return(0,r.jsx)("header",{className:"sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,r.jsx)(l.$,{variant:"ghost",size:"icon",className:"md:hidden",children:(0,r.jsx)(n.A,{className:"w-5 h-5"})}),(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)("h1",{className:"text-xl font-bold",children:"NextGen Store"})}),(0,r.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[(0,r.jsx)("a",{href:"#",className:"text-sm font-medium hover:text-primary transition-colors",children:"New Arrivals"}),(0,r.jsx)("a",{href:"#",className:"text-sm font-medium hover:text-primary transition-colors",children:"Clothing"}),(0,r.jsx)("a",{href:"#",className:"text-sm font-medium hover:text-primary transition-colors",children:"Electronics"}),(0,r.jsx)("a",{href:"#",className:"text-sm font-medium hover:text-primary transition-colors",children:"Accessories"}),(0,r.jsx)("a",{href:"#",className:"text-sm font-medium hover:text-primary transition-colors",children:"Sustainable"})]}),(0,r.jsx)("div",{className:"hidden md:flex items-center flex-1 max-w-md mx-6",children:(0,r.jsxs)("div",{className:"relative w-full",children:[(0,r.jsx)(i.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4"}),(0,r.jsx)("input",{type:"text",placeholder:"Search products...",className:"w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(l.$,{variant:"ghost",size:"icon",className:"md:hidden",children:(0,r.jsx)(i.A,{className:"w-5 h-5"})}),(0,r.jsx)(l.$,{variant:"ghost",size:"icon",children:(0,r.jsx)(o.A,{className:"w-5 h-5"})}),(0,r.jsx)(l.$,{variant:"ghost",size:"icon",children:(0,r.jsx)(a.A,{className:"w-5 h-5"})}),(0,r.jsx)(x,{})]})]}),(0,r.jsx)("div",{className:"md:hidden pb-4",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(i.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4"}),(0,r.jsx)("input",{type:"text",placeholder:"Search products...",className:"w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"})]})})]})})}},9112:(e,t,s)=>{Promise.resolve().then(s.bind(s,494)),Promise.resolve().then(s.bind(s,8832))},9523:(e,t,s)=>{"use strict";s.d(t,{$:()=>o});var r=s(687),n=s(3210),i=s(4780);let o=n.forwardRef(({className:e,variant:t="default",size:s="default",...n},o)=>(0,r.jsx)("button",{className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"}[t],{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[s],e),ref:o,...n}));o.displayName="Button"}};