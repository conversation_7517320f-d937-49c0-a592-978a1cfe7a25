'use client';

import { useState, useEffect } from 'react';
import { Clock, X, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { formatPrice, getTimeUntilExpiry } from '@/lib/utils';

interface FlashSale {
  id: string;
  productName: string;
  originalPrice: number;
  salePrice: number;
  discount: number;
  expiresAt: string;
  reason: string;
  isPersonalized: boolean;
}

export function FlashSaleBanner() {
  const [isVisible, setIsVisible] = useState(true);
  const [timeLeft, setTimeLeft] = useState({ hours: 0, minutes: 0, seconds: 0, expired: false });

  // Simulated personalized flash sale
  const flashSale: FlashSale = {
    id: '1',
    productName: 'Eco-Friendly Cotton T-Shirt',
    originalPrice: 29.99,
    salePrice: 24.99,
    discount: 17,
    expiresAt: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // 10 minutes from now
    reason: 'You viewed this item 3 times last week',
    isPersonalized: true
  };

  useEffect(() => {
    const timer = setInterval(() => {
      const time = getTimeUntilExpiry(flashSale.expiresAt);
      setTimeLeft(time);
      
      if (time.expired) {
        setIsVisible(false);
        clearInterval(timer);
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [flashSale.expiresAt]);

  if (!isVisible || timeLeft.expired) {
    return null;
  }

  return (
    <div className="bg-gradient-to-r from-red-500 to-pink-500 text-white p-4 relative">
      <div className="container mx-auto">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Zap className="w-5 h-5 animate-pulse" />
              <Badge className="bg-white/20 text-white border-white/30">
                PERSONALIZED FLASH SALE
              </Badge>
            </div>
            
            <div className="hidden md:block">
              <p className="text-sm opacity-90">{flashSale.reason}</p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="text-center">
              <p className="font-semibold">{flashSale.productName}</p>
              <div className="flex items-center gap-2">
                <span className="line-through text-sm opacity-75">
                  {formatPrice(flashSale.originalPrice)}
                </span>
                <span className="font-bold text-lg">
                  {formatPrice(flashSale.salePrice)}
                </span>
                <Badge className="bg-yellow-400 text-black">
                  {flashSale.discount}% OFF
                </Badge>
              </div>
            </div>

            <div className="flex items-center gap-2 bg-white/20 rounded-lg px-3 py-2">
              <Clock className="w-4 h-4" />
              <div className="text-center">
                <div className="font-mono font-bold">
                  {String(timeLeft.hours).padStart(2, '0')}:
                  {String(timeLeft.minutes).padStart(2, '0')}:
                  {String(timeLeft.seconds).padStart(2, '0')}
                </div>
                <div className="text-xs opacity-75">Time left</div>
              </div>
            </div>

            <Button 
              className="bg-white text-red-500 hover:bg-gray-100 font-semibold"
              onClick={() => {
                // In a real app, this would add the item to cart with the sale price
                alert('Added to cart with flash sale price!');
              }}
            >
              Claim Deal
            </Button>

            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsVisible(false)}
              className="text-white hover:bg-white/20"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Mobile layout */}
        <div className="md:hidden mt-3 space-y-2">
          <p className="text-sm opacity-90">{flashSale.reason}</p>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-semibold">{flashSale.productName}</p>
              <div className="flex items-center gap-2">
                <span className="line-through text-sm opacity-75">
                  {formatPrice(flashSale.originalPrice)}
                </span>
                <span className="font-bold">
                  {formatPrice(flashSale.salePrice)}
                </span>
                <Badge className="bg-yellow-400 text-black text-xs">
                  {flashSale.discount}% OFF
                </Badge>
              </div>
            </div>
            <Button 
              size="sm"
              className="bg-white text-red-500 hover:bg-gray-100 font-semibold"
              onClick={() => {
                alert('Added to cart with flash sale price!');
              }}
            >
              Claim Deal
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
