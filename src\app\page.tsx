import { ProductGrid } from "@/components/product/product-grid";
import { sampleProducts } from "@/data/products";
import { Badge } from "@/components/ui/badge";
import { FlashSaleBanner } from "@/components/features/flash-sale-banner";
import { Sparkles, Leaf, Zap } from "lucide-react";

export default function Home() {
  return (
    <>
      <FlashSaleBanner />
      <div className="container mx-auto px-4 py-8">
      {/* Hero Section */}
      <section className="text-center py-12 mb-12">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
            Welcome to NextGen Store
          </h1>
          <p className="text-xl text-muted-foreground mb-8">
            Experience the future of shopping with AI-powered recommendations,
            sustainable choices, and personalized experiences.
          </p>
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <Badge variant="secondary" className="px-4 py-2">
              <Sparkles className="w-4 h-4 mr-2" />
              AI-Powered
            </Badge>
            <Badge variant="secondary" className="px-4 py-2">
              <Leaf className="w-4 h-4 mr-2" />
              Eco-Friendly
            </Badge>
            <Badge variant="secondary" className="px-4 py-2">
              <Zap className="w-4 h-4 mr-2" />
              Smart Shopping
            </Badge>
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section>
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold">Featured Products</h2>
          <p className="text-muted-foreground">
            Discover our curated selection of premium items
          </p>
        </div>

        <ProductGrid products={sampleProducts} />
      </section>

      {/* Features Section */}
      <section className="py-16 mt-16 border-t">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Why Choose NextGen Store?</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            We&apos;re not just another online store. We&apos;re pioneering the future of eCommerce
            with cutting-edge technology and sustainable practices.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          <div className="text-center p-6">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Sparkles className="w-8 h-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-2">AI Personal Shopper</h3>
            <p className="text-muted-foreground">
              Our emotion-aware AI chatbot understands your mood and preferences
              to recommend the perfect products for you.
            </p>
          </div>

          <div className="text-center p-6">
            <div className="w-16 h-16 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Leaf className="w-8 h-8 text-green-500" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Sustainable Shopping</h3>
            <p className="text-muted-foreground">
              Every product shows its eco-rating and carbon footprint,
              helping you make environmentally conscious choices.
            </p>
          </div>

          <div className="text-center p-6">
            <div className="w-16 h-16 bg-blue-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Zap className="w-8 h-8 text-blue-500" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Smart Features</h3>
            <p className="text-muted-foreground">
              From virtual try-on to personalized flash sales,
              experience shopping like never before.
            </p>
          </div>
        </div>
      </section>
      </div>
    </>
  );
}
