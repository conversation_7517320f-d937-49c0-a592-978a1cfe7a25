import { type ClassValue, clsx } from "clsx";

export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}

export function formatPrice(price: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(price);
}

export function formatRating(rating: number): string {
  return rating.toFixed(1);
}

export function getEcoRatingColor(rating: 'A' | 'B' | 'C' | 'D' | 'E'): string {
  const colors = {
    A: 'bg-green-500',
    B: 'bg-lime-500',
    C: 'bg-yellow-500',
    D: 'bg-orange-500',
    E: 'bg-red-500',
  };
  return colors[rating];
}

export function getEcoRatingText(rating: 'A' | 'B' | 'C' | 'D' | 'E'): string {
  const texts = {
    A: 'Excellent',
    B: 'Very Good',
    C: 'Good',
    D: 'Fair',
    E: 'Poor',
  };
  return texts[rating];
}

export function calculateCarbonFootprint(weight: number, distance: number): number {
  // Simplified calculation: kg CO2 = weight (kg) * distance (km) * emission factor
  const emissionFactor = 0.0001; // kg CO2 per kg per km (simplified)
  return weight * distance * emissionFactor;
}

export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substr(0, maxLength) + '...';
}

export function getTimeUntilExpiry(expiryDate: string): {
  hours: number;
  minutes: number;
  seconds: number;
  expired: boolean;
} {
  const now = new Date().getTime();
  const expiry = new Date(expiryDate).getTime();
  const difference = expiry - now;

  if (difference <= 0) {
    return { hours: 0, minutes: 0, seconds: 0, expired: true };
  }

  const hours = Math.floor(difference / (1000 * 60 * 60));
  const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((difference % (1000 * 60)) / 1000);

  return { hours, minutes, seconds, expired: false };
}

export function getColorName(hex: string): string {
  const colors: Record<string, string> = {
    '#000000': 'Black',
    '#FFFFFF': 'White',
    '#FF0000': 'Red',
    '#00FF00': 'Green',
    '#0000FF': 'Blue',
    '#FFFF00': 'Yellow',
    '#FF00FF': 'Magenta',
    '#00FFFF': 'Cyan',
    '#FFA500': 'Orange',
    '#800080': 'Purple',
    '#FFC0CB': 'Pink',
    '#A52A2A': 'Brown',
    '#808080': 'Gray',
    '#000080': 'Navy',
    '#008000': 'Dark Green',
    '#800000': 'Maroon',
  };

  return colors[hex.toUpperCase()] || hex;
}

export function analyzeUserMood(recentActivity: Array<{ timeSpent: number; type: string }>): 'cheerful' | 'indecisive' | 'frustrated' | 'focused' {
  // Simplified mood analysis based on user behavior
  // In a real app, this would use more sophisticated AI

  if (recentActivity.length === 0) return 'focused';

  const quickDecisions = recentActivity.filter(a => a.timeSpent < 30).length;
  const longBrowsing = recentActivity.filter(a => a.timeSpent > 300).length;
  const backAndForth = recentActivity.filter(a => a.type === 'back_navigation').length;

  if (quickDecisions > longBrowsing && backAndForth < 2) return 'cheerful';
  if (longBrowsing > quickDecisions && backAndForth > 3) return 'indecisive';
  if (backAndForth > 5) return 'frustrated';

  return 'focused';
}
