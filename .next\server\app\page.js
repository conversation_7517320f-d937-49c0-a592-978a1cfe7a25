(()=>{var e={};e.id=974,e.ids=[974],e.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},554:(e,t)=>{"use strict";function r(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return r}})},660:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)|0;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return a},extractInterceptionRouteInformation:function(){return s},isInterceptionRouteAppPath:function(){return i}});let n=r(4722),a=["(..)(..)","(.)","(..)","(...)"];function i(e){return void 0!==e.split("/").find(e=>a.find(t=>e.startsWith(t)))}function s(e){let t,r,i;for(let n of e.split("/"))if(r=a.find(e=>n.startsWith(e))){[t,i]=e.split(r,2);break}if(!t||!r||!i)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":i="/"===t?"/"+i:t+"/"+i;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});i=t.split("/").slice(0,-1).concat(i).join("/");break;case"(...)":i="/"+i;break;case"(..)(..)":let s=t.split("/");if(s.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});i=s.slice(0,-2).concat(i).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:i}}},1658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return d},normalizeMetadataPageToRoute:function(){return m},normalizeMetadataRoute:function(){return f}});let n=r(8304),a=function(e){return e&&e.__esModule?e:{default:e}}(r(8671)),i=r(6341),s=r(4396),o=r(660),l=r(4722),c=r(2958),u=r(5499);function p(e){let t=a.default.dirname(e);if(e.endsWith("/sitemap"))return"";let r="";return t.split("/").some(e=>(0,u.isGroupSegment)(e)||(0,u.isParallelRouteSegment)(e))&&(r=(0,o.djb2Hash)(t).toString(36).slice(0,6)),r}function d(e,t,r){let n=(0,l.normalizeAppPath)(e),o=(0,s.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),u=(0,i.interpolateDynamicPath)(n,t,o),{name:d,ext:f}=a.default.parse(r),m=p(a.default.posix.join(e,d)),h=m?`-${m}`:"";return(0,c.normalizePathSep)(a.default.join(u,`${d}${h}${f}`))}function f(e){if(!(0,n.isMetadataPage)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":r=p(e),!t.endsWith("/route")){let{dir:e,name:n,ext:i}=a.default.parse(t);t=a.default.posix.join(e,`${n}${r?`-${r}`:""}${i}`,"route")}return t}function m(e,t){let r=e.endsWith("/route"),n=r?e.slice(0,-6):e,a=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${a}`)+(r?"/route":"")}},2437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return a}});let n=r(5362);function a(e,t){let r=[],a=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),i=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(a.source),a.flags):a,r);return(e,n)=>{if("string"!=typeof e)return!1;let a=i(e);if(!a)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete a.params[e.name];return{...n,...a.params}}}},2785:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function a(e){let t=new URLSearchParams;for(let[r,a]of Object.entries(e))if(Array.isArray(a))for(let e of a)t.append(r,n(e));else t.set(r,n(a));return t}function i(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return a}})},2958:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return a}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function a(e){return r.test(e)?e.replace(n,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return a}}),r(4827);let n=r(2785);function a(e,t,r){void 0===r&&(r=!0);let a=new URL("http://n"),i=t?new URL(t,a):e.startsWith(".")?new URL("http://n"):a,{pathname:s,searchParams:o,search:l,hash:c,href:u,origin:p}=new URL(e,i);if(p!==a.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:s,query:r?(0,n.searchParamsToUrlQuery)(o):void 0,search:l,hash:c,href:u.slice(p.length)}}},3873:e=>{"use strict";e.exports=require("path")},4396:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return h},getNamedRouteRegex:function(){return m},getRouteRegex:function(){return p},parseParameter:function(){return l}});let n=r(6143),a=r(1437),i=r(3293),s=r(2887),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(o);return t?c(t[2]):c(e)}function c(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function u(e,t,r){let n={},l=1,u=[];for(let p of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=a.INTERCEPTION_ROUTE_MARKERS.find(e=>p.startsWith(e)),s=p.match(o);if(e&&s&&s[2]){let{key:t,optional:r,repeat:a}=c(s[2]);n[t]={pos:l++,repeat:a,optional:r},u.push("/"+(0,i.escapeStringRegexp)(e)+"([^/]+?)")}else if(s&&s[2]){let{key:e,repeat:t,optional:a}=c(s[2]);n[e]={pos:l++,repeat:t,optional:a},r&&s[1]&&u.push("/"+(0,i.escapeStringRegexp)(s[1]));let o=t?a?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&s[1]&&(o=o.substring(1)),u.push(o)}else u.push("/"+(0,i.escapeStringRegexp)(p));t&&s&&s[3]&&u.push((0,i.escapeStringRegexp)(s[3]))}return{parameterizedRoute:u.join(""),groups:n}}function p(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:a=!1}=void 0===t?{}:t,{parameterizedRoute:i,groups:s}=u(e,r,n),o=i;return a||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:s}}function d(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:a,routeKeys:s,keyPrefix:o,backreferenceDuplicateKeys:l}=e,{key:u,optional:p,repeat:d}=c(a),f=u.replace(/\W/g,"");o&&(f=""+o+f);let m=!1;(0===f.length||f.length>30)&&(m=!0),isNaN(parseInt(f.slice(0,1)))||(m=!0),m&&(f=n());let h=f in s;o?s[f]=""+o+u:s[f]=u;let g=r?(0,i.escapeStringRegexp)(r):"";return t=h&&l?"\\k<"+f+">":d?"(?<"+f+">.+?)":"(?<"+f+">[^/]+?)",p?"(?:/"+g+t+")?":"/"+g+t}function f(e,t,r,l,c){let u,p=(u=0,()=>{let e="",t=++u;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),f={},m=[];for(let u of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=a.INTERCEPTION_ROUTE_MARKERS.some(e=>u.startsWith(e)),s=u.match(o);if(e&&s&&s[2])m.push(d({getSafeRouteKey:p,interceptionMarker:s[1],segment:s[2],routeKeys:f,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:c}));else if(s&&s[2]){l&&s[1]&&m.push("/"+(0,i.escapeStringRegexp)(s[1]));let e=d({getSafeRouteKey:p,segment:s[2],routeKeys:f,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:c});l&&s[1]&&(e=e.substring(1)),m.push(e)}else m.push("/"+(0,i.escapeStringRegexp)(u));r&&s&&s[3]&&m.push((0,i.escapeStringRegexp)(s[3]))}return{namedParameterizedRoute:m.join(""),routeKeys:f}}function m(e,t){var r,n,a;let i=f(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(a=t.backreferenceDuplicateKeys)&&a),s=i.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(s+="(?:/)?"),{...p(e,t),namedRegex:"^"+s+"$",routeKeys:i.routeKeys}}function h(e,t){let{parameterizedRoute:r}=u(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:a}=f(e,!1,!1,!1,!1);return{namedRegex:"^"+a+(n?"(?:(/.*)?)":"")+"$"}}},4722:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return i},normalizeRscURL:function(){return s}});let n=r(5531),a=r(5499);function i(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,a.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function s(e){return e.replace(/\.rsc($|\?)/,"$1")}},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return x},NormalizeError:function(){return h},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return f},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return o},isAbsoluteUrl:function(){return i},isResSent:function(){return c},loadGetInitialProps:function(){return p},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return v}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,a=Array(n),i=0;i<n;i++)a[i]=arguments[i];return r||(r=!0,t=e(...a)),t}}let a=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>a.test(e);function s(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function o(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function u(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function p(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await p(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let d="undefined"!=typeof performance,f=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class h extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class x extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var a="",i=r+1;i<e.length;){var s=e.charCodeAt(i);if(s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122||95===s){a+=e[i++];continue}break}if(!a)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:a}),r=i;continue}if("("===n){var o=1,l="",i=r+1;if("?"===e[i])throw TypeError('Pattern cannot start with "?" at '+i);for(;i<e.length;){if("\\"===e[i]){l+=e[i++]+e[i++];continue}if(")"===e[i]){if(0==--o){i++;break}}else if("("===e[i]&&(o++,"?"!==e[i+1]))throw TypeError("Capturing groups are not allowed at "+i);l+=e[i++]}if(o)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=i;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,i=void 0===n?"./":n,s="[^"+a(t.delimiter||"/#?")+"]+?",o=[],l=0,c=0,u="",p=function(e){if(c<r.length&&r[c].type===e)return r[c++].value},d=function(e){var t=p(e);if(void 0!==t)return t;var n=r[c];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},f=function(){for(var e,t="";e=p("CHAR")||p("ESCAPED_CHAR");)t+=e;return t};c<r.length;){var m=p("CHAR"),h=p("NAME"),g=p("PATTERN");if(h||g){var x=m||"";-1===i.indexOf(x)&&(u+=x,x=""),u&&(o.push(u),u=""),o.push({name:h||l++,prefix:x,suffix:"",pattern:g||s,modifier:p("MODIFIER")||""});continue}var y=m||p("ESCAPED_CHAR");if(y){u+=y;continue}if(u&&(o.push(u),u=""),p("OPEN")){var x=f(),v=p("NAME")||"",b=p("PATTERN")||"",j=f();d("CLOSE"),o.push({name:v||(b?l++:""),pattern:v&&!b?s:b,prefix:x,suffix:j,modifier:p("MODIFIER")||""});continue}d("END")}return o}function r(e,t){void 0===t&&(t={});var r=i(t),n=t.encode,a=void 0===n?function(e){return e}:n,s=t.validate,o=void 0===s||s,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var i=e[n];if("string"==typeof i){r+=i;continue}var s=t?t[i.name]:void 0,c="?"===i.modifier||"*"===i.modifier,u="*"===i.modifier||"+"===i.modifier;if(Array.isArray(s)){if(!u)throw TypeError('Expected "'+i.name+'" to not repeat, but got an array');if(0===s.length){if(c)continue;throw TypeError('Expected "'+i.name+'" to not be empty')}for(var p=0;p<s.length;p++){var d=a(s[p],i);if(o&&!l[n].test(d))throw TypeError('Expected all "'+i.name+'" to match "'+i.pattern+'", but got "'+d+'"');r+=i.prefix+d+i.suffix}continue}if("string"==typeof s||"number"==typeof s){var d=a(String(s),i);if(o&&!l[n].test(d))throw TypeError('Expected "'+i.name+'" to match "'+i.pattern+'", but got "'+d+'"');r+=i.prefix+d+i.suffix;continue}if(!c){var f=u?"an array":"a string";throw TypeError('Expected "'+i.name+'" to be '+f)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,a=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var i=n[0],s=n.index,o=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?o[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return a(e,r)}):o[r.name]=a(n[e],r)}}(l);return{path:i,index:s,params:o}}}function a(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function i(e){return e&&e.sensitive?"":"i"}function s(e,t,r){void 0===r&&(r={});for(var n=r.strict,s=void 0!==n&&n,o=r.start,l=r.end,c=r.encode,u=void 0===c?function(e){return e}:c,p="["+a(r.endsWith||"")+"]|$",d="["+a(r.delimiter||"/#?")+"]",f=void 0===o||o?"^":"",m=0;m<e.length;m++){var h=e[m];if("string"==typeof h)f+=a(u(h));else{var g=a(u(h.prefix)),x=a(u(h.suffix));if(h.pattern)if(t&&t.push(h),g||x)if("+"===h.modifier||"*"===h.modifier){var y="*"===h.modifier?"?":"";f+="(?:"+g+"((?:"+h.pattern+")(?:"+x+g+"(?:"+h.pattern+"))*)"+x+")"+y}else f+="(?:"+g+"("+h.pattern+")"+x+")"+h.modifier;else f+="("+h.pattern+")"+h.modifier;else f+="(?:"+g+x+")"+h.modifier}}if(void 0===l||l)s||(f+=d+"?"),f+=r.endsWith?"(?="+p+")":"$";else{var v=e[e.length-1],b="string"==typeof v?d.indexOf(v[v.length-1])>-1:void 0===v;s||(f+="(?:"+d+"(?="+p+"))?"),b||(f+="(?="+d+"|"+p+")")}return new RegExp(f,i(r))}function o(t,r,n){if(t instanceof RegExp){if(!r)return t;var a=t.source.match(/\((?!\?)/g);if(a)for(var l=0;l<a.length;l++)r.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return o(e,r,n).source}).join("|")+")",i(n)):s(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(o(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=s,t.pathToRegexp=o})(),e.exports=t})()},5526:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return u},matchHas:function(){return c},parseDestination:function(){return p},prepareDestination:function(){return d}});let n=r(5362),a=r(3293),i=r(6759),s=r(1437),o=r(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let a={},i=r=>{let n,i=r.key;switch(r.type){case"header":i=i.toLowerCase(),n=e.headers[i];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,o.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[i];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return a[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(i)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{a[e]=t.groups[e]}):"host"===r.type&&t[0]&&(a.host=t[0])),!0}return!1};return!(!r.every(e=>i(e))||n.some(e=>i(e)))&&a}function u(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function p(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,a.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,i.parseUrl)(t),n=r.pathname;n&&(n=l(n));let s=r.href;s&&(s=l(s));let o=r.hostname;o&&(o=l(o));let c=r.hash;return c&&(c=l(c)),{...r,pathname:n,hostname:o,href:s,hash:c}}function d(e){let t,r,a=Object.assign({},e.query),i=p(e),{hostname:o,query:c}=i,d=i.pathname;i.hash&&(d=""+d+i.hash);let f=[],m=[];for(let e of((0,n.pathToRegexp)(d,m),m))f.push(e.name);if(o){let e=[];for(let t of((0,n.pathToRegexp)(o,e),e))f.push(t.name)}let h=(0,n.compile)(d,{validate:!1});for(let[r,a]of(o&&(t=(0,n.compile)(o,{validate:!1})),Object.entries(c)))Array.isArray(a)?c[r]=a.map(t=>u(l(t),e.params)):"string"==typeof a&&(c[r]=u(l(a),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>f.includes(e)))for(let t of g)t in c||(c[t]=e.params[t]);if((0,s.isInterceptionRouteAppPath)(d))for(let t of d.split("/")){let r=s.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,a]=(r=h(e.params)).split("#",2);t&&(i.hostname=t(e.params)),i.pathname=n,i.hash=(a?"#":"")+(a||""),delete i.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return i.query={...a,...i.query},{newUrl:r,destQuery:c,parsedDestination:i}}},5531:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},6341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPreviouslyRevalidatedTags:function(){return x},getUtils:function(){return g},interpolateDynamicPath:function(){return m},normalizeDynamicRouteParams:function(){return h},normalizeVercelUrl:function(){return f}});let n=r(9551),a=r(1959),i=r(2437),s=r(4396),o=r(8034),l=r(5526),c=r(2887),u=r(4722),p=r(6143),d=r(7912);function f(e,t,r){let a=(0,n.parse)(e.url,!0);for(let e of(delete a.search,Object.keys(a.query))){let n=e!==p.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(p.NEXT_QUERY_PARAM_PREFIX),i=e!==p.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(p.NEXT_INTERCEPTION_MARKER_PREFIX);(n||i||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete a.query[e]}e.url=(0,n.format)(a)}function m(e,t,r){if(!r)return e;for(let n of Object.keys(r.groups)){let a,{optional:i,repeat:s}=r.groups[n],o=`[${s?"...":""}${n}]`;i&&(o=`[${o}]`);let l=t[n];a=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(o,a)}return e}function h(e,t,r,n){let a={};for(let i of Object.keys(t.groups)){let s=e[i];"string"==typeof s?s=(0,u.normalizeRscURL)(s):Array.isArray(s)&&(s=s.map(u.normalizeRscURL));let o=r[i],l=t.groups[i].optional;if((Array.isArray(o)?o.some(e=>Array.isArray(s)?s.some(t=>t.includes(e)):null==s?void 0:s.includes(e)):null==s?void 0:s.includes(o))||void 0===s&&!(l&&n))return{params:{},hasValidParams:!1};l&&(!s||Array.isArray(s)&&1===s.length&&("index"===s[0]||s[0]===`[[...${i}]]`))&&(s=void 0,delete e[i]),s&&"string"==typeof s&&t.groups[i].repeat&&(s=s.split("/")),s&&(a[i]=s)}return{params:a,hasValidParams:!0}}function g({page:e,i18n:t,basePath:r,rewrites:n,pageIsDynamic:u,trailingSlash:p,caseSensitive:g}){let x,y,v;return u&&(x=(0,s.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),v=(y=(0,o.getRouteMatcher)(x))(e)),{handleRewrites:function(s,o){let d={},f=o.pathname,m=n=>{let c=(0,i.getPathMatch)(n.source+(p?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!o.pathname)return!1;let m=c(o.pathname);if((n.has||n.missing)&&m){let e=(0,l.matchHas)(s,o.query,n.has,n.missing);e?Object.assign(m,e):m=!1}if(m){let{parsedDestination:i,destQuery:s}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:m,query:o.query});if(i.protocol)return!0;if(Object.assign(d,s,m),Object.assign(o.query,i.query),delete i.query,Object.assign(o,i),!(f=o.pathname))return!1;if(r&&(f=f.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,a.normalizeLocalePath)(f,t.locales);f=e.pathname,o.query.nextInternalLocale=e.detectedLocale||m.nextInternalLocale}if(f===e)return!0;if(u&&y){let e=y(f);if(e)return o.query={...o.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])m(e);if(f!==e){let t=!1;for(let e of n.afterFiles||[])if(t=m(e))break;if(!t&&!(()=>{let t=(0,c.removeTrailingSlash)(f||"");return t===(0,c.removeTrailingSlash)(e)||(null==y?void 0:y(t))})()){for(let e of n.fallback||[])if(t=m(e))break}}return d},defaultRouteRegex:x,dynamicRouteMatcher:y,defaultRouteMatches:v,getParamsFromRouteMatches:function(e){if(!x)return null;let{groups:t,routeKeys:r}=x,n=(0,o.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let r=(0,d.normalizeNextQueryParam)(e);r&&(n[r]=t,delete n[e])}let a={};for(let e of Object.keys(r)){let i=r[e];if(!i)continue;let s=t[i],o=n[e];if(!s.optional&&!o)return null;a[s.pos]=o}return a}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>x&&v?h(e,x,v,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>f(e,t,x),interpolateDynamicPath:(e,t)=>m(e,t,x)}}function x(e,t){return"string"==typeof e[p.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[p.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[p.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var a={},i=t.split(n),s=(r||{}).decode||e,o=0;o<i.length;o++){var l=i[o],c=l.indexOf("=");if(!(c<0)){var u=l.substr(0,c).trim(),p=l.substr(++c,l.length).trim();'"'==p[0]&&(p=p.slice(1,-1)),void 0==a[u]&&(a[u]=function(e,t){try{return t(e)}catch(t){return e}}(p,s))}}return a},t.serialize=function(e,t,n){var i=n||{},s=i.encode||r;if("function"!=typeof s)throw TypeError("option encode is invalid");if(!a.test(e))throw TypeError("argument name is invalid");var o=s(t);if(o&&!a.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=i.maxAge){var c=i.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(i.domain){if(!a.test(i.domain))throw TypeError("option domain is invalid");l+="; Domain="+i.domain}if(i.path){if(!a.test(i.path))throw TypeError("option path is invalid");l+="; Path="+i.path}if(i.expires){if("function"!=typeof i.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+i.expires.toUTCString()}if(i.httpOnly&&(l+="; HttpOnly"),i.secure&&(l+="; Secure"),i.sameSite)switch("string"==typeof i.sameSite?i.sameSite.toLowerCase():i.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,a=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6590:(e,t,r)=>{Promise.resolve().then(r.bind(r,9479))},6678:(e,t,r)=>{Promise.resolve().then(r.bind(r,7936))},6759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return i}});let n=r(2785),a=r(3736);function i(e){if(e.startsWith("/"))return(0,a.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},7408:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>p,pages:()=>u,routeModule:()=>d,tree:()=>c});var n=r(5239),a=r(8088),i=r(8170),s=r.n(i),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8450)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\app\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7936:(e,t,r)=>{"use strict";r.d(t,{ProductGrid:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call ProductGrid() from the server but ProductGrid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\AI Internship\\e-commerence\\src\\components\\product\\product-grid.tsx","ProductGrid")},8034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return a}});let n=r(4827);function a(e){let{re:t,groups:r}=e;return e=>{let a=t.exec(e);if(!a)return!1;let i=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},s={};for(let[e,t]of Object.entries(r)){let r=a[t.pos];void 0!==r&&(t.repeat?s[e]=r.split("/").map(e=>i(e)):s[e]=i(r))}return s}}},8212:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(6415);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},8304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return o},STATIC_METADATA_IMAGES:function(){return s},getExtensionRegexString:function(){return l},isMetadataPage:function(){return p},isMetadataRoute:function(){return d},isMetadataRouteFile:function(){return c},isStaticMetadataRoute:function(){return u}});let n=r(2958),a=r(4722),i=r(554),s={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function c(e,t,r){let a=(r?"":"?")+"$",i=`\\d?${r?"":"(-\\w{6})?"}`,o=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${a}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${a}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${a}`),RegExp(`[\\\\/]${s.icon.filename}${i}${l(s.icon.extensions,t)}${a}`),RegExp(`[\\\\/]${s.apple.filename}${i}${l(s.apple.extensions,t)}${a}`),RegExp(`[\\\\/]${s.openGraph.filename}${i}${l(s.openGraph.extensions,t)}${a}`),RegExp(`[\\\\/]${s.twitter.filename}${i}${l(s.twitter.extensions,t)}${a}`)],c=(0,n.normalizePathSep)(e);return o.some(e=>e.test(c))}function u(e){let t=e.replace(/\/route$/,"");return(0,i.isAppRouteRoute)(e)&&c(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function p(e){return!(0,i.isAppRouteRoute)(e)&&c(e,[],!1)}function d(e){let t=(0,a.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,i.isAppRouteRoute)(e)&&c(t,[],!1)}},8450:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var n=r(7413),a=r(7936);let i=[{id:"1",name:"Eco-Friendly Cotton T-Shirt",price:29.99,images:["https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=600&fit=crop","https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=500&h=600&fit=crop","https://images.unsplash.com/photo-1562157873-818bc0726f68?w=500&h=600&fit=crop"],colors:[{name:"Black",value:"#000000",image:"https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=600&fit=crop"},{name:"White",value:"#FFFFFF",image:"https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=500&h=600&fit=crop"},{name:"Navy",value:"#000080",image:"https://images.unsplash.com/photo-1562157873-818bc0726f68?w=500&h=600&fit=crop"}],sizes:["XS","S","M","L","XL","XXL"],description:"Made from 100% organic cotton, this comfortable t-shirt is perfect for everyday wear. Sustainably sourced and ethically manufactured.",category:"Clothing",inStock:!0,rating:4.5,reviews:128,ecoRating:"A",carbonFootprint:2.1,brand:"EcoWear",material:"100% Organic Cotton",careInstructions:["Machine wash cold","Tumble dry low","Do not bleach"],tags:["sustainable","organic","basic","casual"]},{id:"2",name:"Premium Denim Jeans",price:89.99,images:["https://images.unsplash.com/photo-1542272604-787c3835535d?w=500&h=600&fit=crop","https://images.unsplash.com/photo-1475178626620-a4d074967452?w=500&h=600&fit=crop","https://images.unsplash.com/photo-1506629905607-d9b1e5b6e8b7?w=500&h=600&fit=crop"],colors:[{name:"Dark Blue",value:"#1e3a8a",image:"https://images.unsplash.com/photo-1542272604-787c3835535d?w=500&h=600&fit=crop"},{name:"Light Blue",value:"#3b82f6",image:"https://images.unsplash.com/photo-1475178626620-a4d074967452?w=500&h=600&fit=crop"},{name:"Black",value:"#000000",image:"https://images.unsplash.com/photo-1506629905607-d9b1e5b6e8b7?w=500&h=600&fit=crop"}],sizes:["28","30","32","34","36","38","40"],description:"Classic fit denim jeans made with premium cotton blend. Features a comfortable mid-rise waist and timeless straight-leg cut.",category:"Clothing",inStock:!0,rating:4.3,reviews:89,ecoRating:"C",carbonFootprint:8.5,brand:"DenimCo",material:"98% Cotton, 2% Elastane",careInstructions:["Machine wash cold","Hang dry","Iron on medium heat"],tags:["denim","classic","versatile"]},{id:"3",name:"Wireless Bluetooth Headphones",price:199.99,images:["https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500&h=600&fit=crop","https://images.unsplash.com/photo-1484704849700-f032a568e944?w=500&h=600&fit=crop","https://images.unsplash.com/photo-1583394838336-acd977736f90?w=500&h=600&fit=crop"],colors:[{name:"Black",value:"#000000",image:"https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500&h=600&fit=crop"},{name:"White",value:"#FFFFFF",image:"https://images.unsplash.com/photo-1484704849700-f032a568e944?w=500&h=600&fit=crop"},{name:"Silver",value:"#C0C0C0",image:"https://images.unsplash.com/photo-1583394838336-acd977736f90?w=500&h=600&fit=crop"}],sizes:["One Size"],description:"Premium wireless headphones with active noise cancellation, 30-hour battery life, and crystal-clear audio quality.",category:"Electronics",inStock:!0,rating:4.7,reviews:256,ecoRating:"B",carbonFootprint:12.3,brand:"AudioTech",material:"Plastic, Metal, Foam",careInstructions:["Clean with dry cloth","Store in case when not in use"],tags:["wireless","noise-cancelling","premium","audio"]},{id:"4",name:"Minimalist Leather Wallet",price:49.99,images:["https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=500&h=600&fit=crop","https://images.unsplash.com/photo-1627123424574-724758594e93?w=500&h=600&fit=crop","https://images.unsplash.com/photo-1608686207856-001b95cf60ca?w=500&h=600&fit=crop"],colors:[{name:"Brown",value:"#8B4513",image:"https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=500&h=600&fit=crop"},{name:"Black",value:"#000000",image:"https://images.unsplash.com/photo-1627123424574-724758594e93?w=500&h=600&fit=crop"},{name:"Tan",value:"#D2B48C",image:"https://images.unsplash.com/photo-1608686207856-001b95cf60ca?w=500&h=600&fit=crop"}],sizes:["One Size"],description:"Handcrafted genuine leather wallet with RFID blocking technology. Slim design holds up to 8 cards and cash.",category:"Accessories",inStock:!0,rating:4.4,reviews:67,ecoRating:"B",carbonFootprint:3.2,brand:"LeatherCraft",material:"Genuine Leather",careInstructions:["Clean with leather conditioner","Keep away from water"],tags:["leather","minimalist","RFID","handcrafted"]}];var s=r(1120);function o({className:e,variant:t="default",...r}){return(0,n.jsx)("div",{className:function(...e){return function(){for(var e,t,r=0,n="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=function e(t){var r,n,a="";if("string"==typeof t||"number"==typeof t)a+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(r=0;r<i;r++)t[r]&&(n=e(t[r]))&&(a&&(a+=" "),a+=n)}else for(n in t)t[n]&&(a&&(a+=" "),a+=n);return a}(e))&&(n&&(n+=" "),n+=t);return n}(e)}("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80"}[t],e),...r})}let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),c=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),u=e=>{let t=c(e);return t.charAt(0).toUpperCase()+t.slice(1)},p=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var f={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let m=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:a="",children:i,iconNode:o,...l},c)=>(0,s.createElement)("svg",{ref:c,...f,width:t,height:t,stroke:e,strokeWidth:n?24*Number(r)/Number(t):r,className:p("lucide",a),...!i&&!d(l)&&{"aria-hidden":"true"},...l},[...o.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(i)?i:[i]])),h=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...n},a)=>(0,s.createElement)(m,{ref:a,iconNode:t,className:p(`lucide-${l(u(e))}`,`lucide-${e}`,r),...n}));return r.displayName=u(e),r},g=h("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),x=h("leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]]),y=h("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);function v(){return(0,n.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,n.jsx)("section",{className:"text-center py-12 mb-12",children:(0,n.jsxs)("div",{className:"max-w-3xl mx-auto",children:[(0,n.jsx)("h1",{className:"text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent",children:"Welcome to NextGen Store"}),(0,n.jsx)("p",{className:"text-xl text-muted-foreground mb-8",children:"Experience the future of shopping with AI-powered recommendations, sustainable choices, and personalized experiences."}),(0,n.jsxs)("div",{className:"flex flex-wrap justify-center gap-4 mb-8",children:[(0,n.jsxs)(o,{variant:"secondary",className:"px-4 py-2",children:[(0,n.jsx)(g,{className:"w-4 h-4 mr-2"}),"AI-Powered"]}),(0,n.jsxs)(o,{variant:"secondary",className:"px-4 py-2",children:[(0,n.jsx)(x,{className:"w-4 h-4 mr-2"}),"Eco-Friendly"]}),(0,n.jsxs)(o,{variant:"secondary",className:"px-4 py-2",children:[(0,n.jsx)(y,{className:"w-4 h-4 mr-2"}),"Smart Shopping"]})]})]})}),(0,n.jsxs)("section",{children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,n.jsx)("h2",{className:"text-3xl font-bold",children:"Featured Products"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Discover our curated selection of premium items"})]}),(0,n.jsx)(a.ProductGrid,{products:i})]}),(0,n.jsxs)("section",{className:"py-16 mt-16 border-t",children:[(0,n.jsxs)("div",{className:"text-center mb-12",children:[(0,n.jsx)("h2",{className:"text-3xl font-bold mb-4",children:"Why Choose NextGen Store?"}),(0,n.jsx)("p",{className:"text-muted-foreground max-w-2xl mx-auto",children:"We're not just another online store. We're pioneering the future of eCommerce with cutting-edge technology and sustainable practices."})]}),(0,n.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,n.jsxs)("div",{className:"text-center p-6",children:[(0,n.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)(g,{className:"w-8 h-8 text-primary"})}),(0,n.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"AI Personal Shopper"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Our emotion-aware AI chatbot understands your mood and preferences to recommend the perfect products for you."})]}),(0,n.jsxs)("div",{className:"text-center p-6",children:[(0,n.jsx)("div",{className:"w-16 h-16 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)(x,{className:"w-8 h-8 text-green-500"})}),(0,n.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Sustainable Shopping"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Every product shows its eco-rating and carbon footprint, helping you make environmentally conscious choices."})]}),(0,n.jsxs)("div",{className:"text-center p-6",children:[(0,n.jsx)("div",{className:"w-16 h-16 bg-blue-500/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)(y,{className:"w-8 h-8 text-blue-500"})}),(0,n.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Smart Features"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"From virtual try-on to personalized flash sales, experience shopping like never before."})]})]})]})]})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9479:(e,t,r)=>{"use strict";r.d(t,{ProductGrid:()=>b});var n=r(687),a=r(3210),i=r(474),s=r(2688);let o=(0,s.A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);var l=r(7760);let c=(0,s.A)("leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]]);var u=r(4780);let p=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,u.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));p.displayName="Card",a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,u.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("h3",{ref:r,className:(0,u.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("p",{ref:r,className:(0,u.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let d=a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,u.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent",a.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,u.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter";var f=r(6834),m=r(9523);function h({product:e,onQuickAdd:t,onViewDetails:r}){let s,[h,g]=(0,a.useState)(e.images[0]),[x,y]=(0,a.useState)(null),[v,b]=(0,a.useState)(!1),j=e=>{g(e),y(e)},E=()=>{x&&(g(e.images[0]),y(null))};return(0,n.jsxs)(p,{className:"group relative overflow-hidden transition-all duration-300 hover:shadow-lg",children:[(0,n.jsx)(m.$,{variant:"ghost",size:"icon",className:"absolute top-2 right-2 z-10 bg-white/80 backdrop-blur-sm hover:bg-white",onClick:()=>b(!v),children:(0,n.jsx)(l.A,{className:(0,u.cn)("w-4 h-4 transition-colors",v?"fill-red-500 text-red-500":"text-gray-600")})}),e.ecoRating&&(0,n.jsxs)(f.E,{className:(0,u.cn)("absolute top-2 left-2 z-10 text-white",(0,u.sj)(e.ecoRating)),children:[(0,n.jsx)(c,{className:"w-3 h-3 mr-1"}),e.ecoRating]}),(0,n.jsxs)("div",{className:"relative aspect-square overflow-hidden",children:[(0,n.jsx)(i.default,{src:h,alt:e.name,fill:!0,className:"object-cover transition-transform duration-300 group-hover:scale-105",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}),(0,n.jsx)("div",{className:"absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center p-4",children:(0,n.jsx)(m.$,{onClick:()=>t?.(e),className:"w-full bg-white text-black hover:bg-gray-100",children:"Quick Add"})})]}),(0,n.jsxs)(d,{className:"p-4",children:[e.brand&&(0,n.jsx)("p",{className:"text-xs text-muted-foreground uppercase tracking-wide mb-1",children:e.brand}),(0,n.jsx)("h3",{className:"font-semibold text-sm mb-2 line-clamp-2 cursor-pointer hover:text-primary transition-colors",onClick:()=>r?.(e),children:e.name}),(0,n.jsxs)("div",{className:"flex items-center gap-1 mb-2",children:[(0,n.jsx)("div",{className:"flex",children:(s=e.rating,Array.from({length:5},(e,t)=>(0,n.jsx)(o,{className:(0,u.cn)("w-4 h-4",t<Math.floor(s)?"fill-yellow-400 text-yellow-400":"text-gray-300")},t)))}),(0,n.jsxs)("span",{className:"text-xs text-muted-foreground ml-1",children:["(",e.reviews,")"]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,n.jsx)("span",{className:"text-lg font-bold",children:(0,u.$g)(e.price)}),e.carbonFootprint&&(0,n.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.carbonFootprint,"kg CO₂"]})]}),(0,n.jsx)("div",{className:"flex gap-2 mb-3",children:e.colors.map((e,t)=>(0,n.jsx)("button",{type:"button",className:(0,u.cn)("w-6 h-6 rounded-full border-2 transition-all duration-200",x===e.image?"border-primary scale-110":"border-gray-300 hover:border-gray-400"),style:{backgroundColor:e.value},onMouseEnter:()=>j(e.image),onMouseLeave:E,title:e.name},t))}),(0,n.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.sizes.slice(0,4).map((e,t)=>(0,n.jsx)(f.E,{variant:"outline",className:"text-xs px-2 py-1",children:e},t)),e.sizes.length>4&&(0,n.jsxs)(f.E,{variant:"outline",className:"text-xs px-2 py-1",children:["+",e.sizes.length-4]})]})]})]})}var g=r(1860);let x=(0,s.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var y=r(5801);function v({product:e,isOpen:t,onClose:r}){let s,[l,p]=(0,a.useState)(""),[d,h]=(0,a.useState)(""),[v,b]=(0,a.useState)(""),[j,E]=(0,a.useState)(1),[R,w]=(0,a.useState)(!1),{addItem:N}=(0,y.x)();if(!t||!e)return null;""===v&&e.images.length>0&&b(e.images[0]);let _=(e,t)=>{p(e),b(t),w(!1)},P=e=>{h(e),w(!1)};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"fixed inset-0 bg-black/50 z-50",onClick:r}),(0,n.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold",children:"Select Options"}),(0,n.jsx)(m.$,{variant:"ghost",size:"icon",onClick:r,children:(0,n.jsx)(g.A,{className:"w-5 h-5"})})]}),(0,n.jsx)("div",{className:"p-6",children:(0,n.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"relative aspect-square",children:[(0,n.jsx)(i.default,{src:v,alt:e.name,fill:!0,className:"object-cover rounded-lg"}),e.ecoRating&&(0,n.jsxs)(f.E,{className:(0,u.cn)("absolute top-2 left-2 text-white",(0,u.sj)(e.ecoRating)),children:[(0,n.jsx)(c,{className:"w-3 h-3 mr-1"}),e.ecoRating]})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[e.brand&&(0,n.jsx)("p",{className:"text-sm text-muted-foreground uppercase tracking-wide",children:e.brand}),(0,n.jsx)("h3",{className:"text-2xl font-bold",children:e.name}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{className:"flex",children:(s=e.rating,Array.from({length:5},(e,t)=>(0,n.jsx)(o,{className:(0,u.cn)("w-4 h-4",t<Math.floor(s)?"fill-yellow-400 text-yellow-400":"text-gray-300")},t)))}),(0,n.jsxs)("span",{className:"text-sm text-muted-foreground",children:["(",e.reviews," reviews)"]})]}),(0,n.jsx)("div",{className:"text-3xl font-bold",children:(0,u.$g)(e.price)}),R&&(!l||!d)&&(0,n.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg",children:[(0,n.jsx)(x,{className:"w-4 h-4 text-red-500"}),(0,n.jsx)("span",{className:"text-sm text-red-700",children:"Please select color and size to add product into cart."})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("label",{className:"text-sm font-medium",children:["Color ",!l&&R&&(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("div",{className:"flex gap-2 flex-wrap",children:e.colors.map((e,t)=>(0,n.jsxs)("button",{type:"button",className:(0,u.cn)("flex items-center gap-2 px-3 py-2 border rounded-lg transition-all",l===e.name?"border-primary bg-primary/10":"border-gray-300 hover:border-gray-400"),onClick:()=>_(e.name,e.image),children:[(0,n.jsx)("div",{className:"w-4 h-4 rounded-full border",style:{backgroundColor:e.value}}),(0,n.jsx)("span",{className:"text-sm",children:e.name})]},t))})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("label",{className:"text-sm font-medium",children:["Size ",!d&&R&&(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("div",{className:"flex gap-2 flex-wrap",children:e.sizes.map((e,t)=>(0,n.jsx)("button",{type:"button",className:(0,u.cn)("px-3 py-2 border rounded-lg transition-all text-sm",d===e?"border-primary bg-primary/10":"border-gray-300 hover:border-gray-400"),onClick:()=>P(e),children:e},t))})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{className:"text-sm font-medium",children:"Quantity"}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(m.$,{variant:"outline",size:"icon",onClick:()=>E(Math.max(1,j-1)),children:"-"}),(0,n.jsx)("span",{className:"w-12 text-center",children:j}),(0,n.jsx)(m.$,{variant:"outline",size:"icon",onClick:()=>E(j+1),children:"+"})]})]}),(0,n.jsxs)(m.$,{className:"w-full",size:"lg",onClick:()=>{if(!l||!d)return void w(!0);N(e,l,d,j),p(""),h(""),E(1),w(!1),r()},children:["Add to Cart - ",(0,u.$g)(e.price*j)]}),(0,n.jsxs)("div",{className:"pt-4 border-t",children:[(0,n.jsx)("h4",{className:"font-medium mb-2",children:"Description"}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]})]})]})})]})})]})}function b({products:e}){let[t,r]=(0,a.useState)(null),[i,s]=(0,a.useState)(!1),o=e=>{r(e),s(!0)},l=e=>{r(e),s(!0)};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:e.map(e=>(0,n.jsx)(h,{product:e,onQuickAdd:o,onViewDetails:l},e.id))}),(0,n.jsx)(v,{product:t,isOpen:i,onClose:()=>{s(!1),r(null)}})]})}},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,455,585],()=>r(7408));module.exports=n})();