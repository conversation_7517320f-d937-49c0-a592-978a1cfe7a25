{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/product/product-grid.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProductGrid = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProductGrid() from the server but ProductGrid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/product/product-grid.tsx <module evaluation>\",\n    \"ProductGrid\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,yEACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/product/product-grid.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProductGrid = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProductGrid() from the server but ProductGrid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/product/product-grid.tsx\",\n    \"ProductGrid\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,qDACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/data/products.ts"], "sourcesContent": ["import { Product } from '@/types';\n\nexport const sampleProducts: Product[] = [\n  {\n    id: '1',\n    name: 'Eco-Friendly Cotton T-Shirt',\n    price: 29.99,\n    images: [\n      'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=600&fit=crop',\n      'https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=500&h=600&fit=crop',\n      'https://images.unsplash.com/photo-1562157873-818bc0726f68?w=500&h=600&fit=crop'\n    ],\n    colors: [\n      {\n        name: 'Black',\n        value: '#000000',\n        image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=600&fit=crop'\n      },\n      {\n        name: 'White',\n        value: '#FFFFFF',\n        image: 'https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=500&h=600&fit=crop'\n      },\n      {\n        name: 'Navy',\n        value: '#000080',\n        image: 'https://images.unsplash.com/photo-1562157873-818bc0726f68?w=500&h=600&fit=crop'\n      }\n    ],\n    sizes: ['XS', 'S', 'M', 'L', 'XL', 'XXL'],\n    description: 'Made from 100% organic cotton, this comfortable t-shirt is perfect for everyday wear. Sustainably sourced and ethically manufactured.',\n    category: 'Clothing',\n    inStock: true,\n    rating: 4.5,\n    reviews: 128,\n    ecoRating: 'A',\n    carbonFootprint: 2.1,\n    brand: 'EcoWear',\n    material: '100% Organic Cotton',\n    careInstructions: ['Machine wash cold', 'Tumble dry low', 'Do not bleach'],\n    tags: ['sustainable', 'organic', 'basic', 'casual']\n  },\n  {\n    id: '2',\n    name: 'Premium Denim Jeans',\n    price: 89.99,\n    images: [\n      'https://images.unsplash.com/photo-1542272604-787c3835535d?w=500&h=600&fit=crop',\n      'https://images.unsplash.com/photo-1475178626620-a4d074967452?w=500&h=600&fit=crop',\n      'https://images.unsplash.com/photo-1506629905607-d9b1e5b6e8b7?w=500&h=600&fit=crop'\n    ],\n    colors: [\n      {\n        name: 'Dark Blue',\n        value: '#1e3a8a',\n        image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=500&h=600&fit=crop'\n      },\n      {\n        name: 'Light Blue',\n        value: '#3b82f6',\n        image: 'https://images.unsplash.com/photo-1475178626620-a4d074967452?w=500&h=600&fit=crop'\n      },\n      {\n        name: 'Black',\n        value: '#000000',\n        image: 'https://images.unsplash.com/photo-1506629905607-d9b1e5b6e8b7?w=500&h=600&fit=crop'\n      }\n    ],\n    sizes: ['28', '30', '32', '34', '36', '38', '40'],\n    description: 'Classic fit denim jeans made with premium cotton blend. Features a comfortable mid-rise waist and timeless straight-leg cut.',\n    category: 'Clothing',\n    inStock: true,\n    rating: 4.3,\n    reviews: 89,\n    ecoRating: 'C',\n    carbonFootprint: 8.5,\n    brand: 'DenimCo',\n    material: '98% Cotton, 2% Elastane',\n    careInstructions: ['Machine wash cold', 'Hang dry', 'Iron on medium heat'],\n    tags: ['denim', 'classic', 'versatile']\n  },\n  {\n    id: '3',\n    name: 'Wireless Bluetooth Headphones',\n    price: 199.99,\n    images: [\n      'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500&h=600&fit=crop',\n      'https://images.unsplash.com/photo-1484704849700-f032a568e944?w=500&h=600&fit=crop',\n      'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=500&h=600&fit=crop'\n    ],\n    colors: [\n      {\n        name: 'Black',\n        value: '#000000',\n        image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500&h=600&fit=crop'\n      },\n      {\n        name: 'White',\n        value: '#FFFFFF',\n        image: 'https://images.unsplash.com/photo-1484704849700-f032a568e944?w=500&h=600&fit=crop'\n      },\n      {\n        name: 'Silver',\n        value: '#C0C0C0',\n        image: 'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=500&h=600&fit=crop'\n      }\n    ],\n    sizes: ['One Size'],\n    description: 'Premium wireless headphones with active noise cancellation, 30-hour battery life, and crystal-clear audio quality.',\n    category: 'Electronics',\n    inStock: true,\n    rating: 4.7,\n    reviews: 256,\n    ecoRating: 'B',\n    carbonFootprint: 12.3,\n    brand: 'AudioTech',\n    material: 'Plastic, Metal, Foam',\n    careInstructions: ['Clean with dry cloth', 'Store in case when not in use'],\n    tags: ['wireless', 'noise-cancelling', 'premium', 'audio']\n  },\n  {\n    id: '4',\n    name: 'Minimalist Leather Wallet',\n    price: 49.99,\n    images: [\n      'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=500&h=600&fit=crop',\n      'https://images.unsplash.com/photo-1627123424574-724758594e93?w=500&h=600&fit=crop',\n      'https://images.unsplash.com/photo-1608686207856-001b95cf60ca?w=500&h=600&fit=crop'\n    ],\n    colors: [\n      {\n        name: 'Brown',\n        value: '#8B4513',\n        image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=500&h=600&fit=crop'\n      },\n      {\n        name: 'Black',\n        value: '#000000',\n        image: 'https://images.unsplash.com/photo-1627123424574-724758594e93?w=500&h=600&fit=crop'\n      },\n      {\n        name: 'Tan',\n        value: '#D2B48C',\n        image: 'https://images.unsplash.com/photo-1608686207856-001b95cf60ca?w=500&h=600&fit=crop'\n      }\n    ],\n    sizes: ['One Size'],\n    description: 'Handcrafted genuine leather wallet with RFID blocking technology. Slim design holds up to 8 cards and cash.',\n    category: 'Accessories',\n    inStock: true,\n    rating: 4.4,\n    reviews: 67,\n    ecoRating: 'B',\n    carbonFootprint: 3.2,\n    brand: 'LeatherCraft',\n    material: 'Genuine Leather',\n    careInstructions: ['Clean with leather conditioner', 'Keep away from water'],\n    tags: ['leather', 'minimalist', 'RFID', 'handcrafted']\n  }\n];\n\nexport const categories = [\n  'All',\n  'Clothing',\n  'Electronics',\n  'Accessories',\n  'Home & Garden',\n  'Sports & Outdoors',\n  'Beauty & Personal Care'\n];\n\nexport const brands = [\n  'EcoWear',\n  'DenimCo',\n  'AudioTech',\n  'LeatherCraft',\n  'TechGear',\n  'StyleCo',\n  'GreenLiving'\n];\n\nexport const colors = [\n  { name: 'Black', value: '#000000' },\n  { name: 'White', value: '#FFFFFF' },\n  { name: 'Red', value: '#FF0000' },\n  { name: 'Blue', value: '#0000FF' },\n  { name: 'Green', value: '#008000' },\n  { name: 'Yellow', value: '#FFFF00' },\n  { name: 'Purple', value: '#800080' },\n  { name: 'Orange', value: '#FFA500' },\n  { name: 'Pink', value: '#FFC0CB' },\n  { name: 'Brown', value: '#8B4513' },\n  { name: 'Gray', value: '#808080' },\n  { name: 'Navy', value: '#000080' }\n];\n\nexport const sizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL', '28', '30', '32', '34', '36', '38', '40', 'One Size'];\n"], "names": [], "mappings": ";;;;;;;AAEO,MAAM,iBAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;YACN;YACA;YACA;SACD;QACD,QAAQ;YACN;gBACE,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;SACD;QACD,OAAO;YAAC;YAAM;YAAK;YAAK;YAAK;YAAM;SAAM;QACzC,aAAa;QACb,UAAU;QACV,SAAS;QACT,QAAQ;QACR,SAAS;QACT,WAAW;QACX,iBAAiB;QACjB,OAAO;QACP,UAAU;QACV,kBAAkB;YAAC;YAAqB;YAAkB;SAAgB;QAC1E,MAAM;YAAC;YAAe;YAAW;YAAS;SAAS;IACrD;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;YACN;YACA;YACA;SACD;QACD,QAAQ;YACN;gBACE,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;SACD;QACD,OAAO;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK;QACjD,aAAa;QACb,UAAU;QACV,SAAS;QACT,QAAQ;QACR,SAAS;QACT,WAAW;QACX,iBAAiB;QACjB,OAAO;QACP,UAAU;QACV,kBAAkB;YAAC;YAAqB;YAAY;SAAsB;QAC1E,MAAM;YAAC;YAAS;YAAW;SAAY;IACzC;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;YACN;YACA;YACA;SACD;QACD,QAAQ;YACN;gBACE,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;SACD;QACD,OAAO;YAAC;SAAW;QACnB,aAAa;QACb,UAAU;QACV,SAAS;QACT,QAAQ;QACR,SAAS;QACT,WAAW;QACX,iBAAiB;QACjB,OAAO;QACP,UAAU;QACV,kBAAkB;YAAC;YAAwB;SAAgC;QAC3E,MAAM;YAAC;YAAY;YAAoB;YAAW;SAAQ;IAC5D;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;YACN;YACA;YACA;SACD;QACD,QAAQ;YACN;gBACE,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;SACD;QACD,OAAO;YAAC;SAAW;QACnB,aAAa;QACb,UAAU;QACV,SAAS;QACT,QAAQ;QACR,SAAS;QACT,WAAW;QACX,iBAAiB;QACjB,OAAO;QACP,UAAU;QACV,kBAAkB;YAAC;YAAkC;SAAuB;QAC5E,MAAM;YAAC;YAAW;YAAc;YAAQ;SAAc;IACxD;CACD;AAEM,MAAM,aAAa;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,SAAS;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,SAAS;IACpB;QAAE,MAAM;QAAS,OAAO;IAAU;IAClC;QAAE,MAAM;QAAS,OAAO;IAAU;IAClC;QAAE,MAAM;QAAO,OAAO;IAAU;IAChC;QAAE,MAAM;QAAQ,OAAO;IAAU;IACjC;QAAE,MAAM;QAAS,OAAO;IAAU;IAClC;QAAE,MAAM;QAAU,OAAO;IAAU;IACnC;QAAE,MAAM;QAAU,OAAO;IAAU;IACnC;QAAE,MAAM;QAAU,OAAO;IAAU;IACnC;QAAE,MAAM;QAAQ,OAAO;IAAU;IACjC;QAAE,MAAM;QAAS,OAAO;IAAU;IAClC;QAAE,MAAM;QAAQ,OAAO;IAAU;IACjC;QAAE,MAAM;QAAQ,OAAO;IAAU;CAClC;AAEM,MAAM,QAAQ;IAAC;IAAM;IAAK;IAAK;IAAK;IAAM;IAAO;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;CAAW", "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price);\n}\n\nexport function formatRating(rating: number): string {\n  return rating.toFixed(1);\n}\n\nexport function getEcoRatingColor(rating: 'A' | 'B' | 'C' | 'D' | 'E'): string {\n  const colors = {\n    A: 'bg-green-500',\n    B: 'bg-lime-500',\n    C: 'bg-yellow-500',\n    D: 'bg-orange-500',\n    E: 'bg-red-500',\n  };\n  return colors[rating];\n}\n\nexport function getEcoRatingText(rating: 'A' | 'B' | 'C' | 'D' | 'E'): string {\n  const texts = {\n    A: 'Excellent',\n    B: 'Very Good',\n    C: 'Good',\n    D: 'Fair',\n    E: 'Poor',\n  };\n  return texts[rating];\n}\n\nexport function calculateCarbonFootprint(weight: number, distance: number): number {\n  // Simplified calculation: kg CO2 = weight (kg) * distance (km) * emission factor\n  const emissionFactor = 0.0001; // kg CO2 per kg per km (simplified)\n  return weight * distance * emissionFactor;\n}\n\nexport function debounce<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substr(0, maxLength) + '...';\n}\n\nexport function getTimeUntilExpiry(expiryDate: string): {\n  hours: number;\n  minutes: number;\n  seconds: number;\n  expired: boolean;\n} {\n  const now = new Date().getTime();\n  const expiry = new Date(expiryDate).getTime();\n  const difference = expiry - now;\n\n  if (difference <= 0) {\n    return { hours: 0, minutes: 0, seconds: 0, expired: true };\n  }\n\n  const hours = Math.floor(difference / (1000 * 60 * 60));\n  const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));\n  const seconds = Math.floor((difference % (1000 * 60)) / 1000);\n\n  return { hours, minutes, seconds, expired: false };\n}\n\nexport function getColorName(hex: string): string {\n  const colors: Record<string, string> = {\n    '#000000': 'Black',\n    '#FFFFFF': 'White',\n    '#FF0000': 'Red',\n    '#00FF00': 'Green',\n    '#0000FF': 'Blue',\n    '#FFFF00': 'Yellow',\n    '#FF00FF': 'Magenta',\n    '#00FFFF': 'Cyan',\n    '#FFA500': 'Orange',\n    '#800080': 'Purple',\n    '#FFC0CB': 'Pink',\n    '#A52A2A': 'Brown',\n    '#808080': 'Gray',\n    '#000080': 'Navy',\n    '#008000': 'Dark Green',\n    '#800000': 'Maroon',\n  };\n\n  return colors[hex.toUpperCase()] || hex;\n}\n\nexport function analyzeUserMood(recentActivity: Array<{ timeSpent: number; type: string }>): 'cheerful' | 'indecisive' | 'frustrated' | 'focused' {\n  // Simplified mood analysis based on user behavior\n  // In a real app, this would use more sophisticated AI\n\n  if (recentActivity.length === 0) return 'focused';\n\n  const quickDecisions = recentActivity.filter(a => a.timeSpent < 30).length;\n  const longBrowsing = recentActivity.filter(a => a.timeSpent > 300).length;\n  const backAndForth = recentActivity.filter(a => a.type === 'back_navigation').length;\n\n  if (quickDecisions > longBrowsing && backAndForth < 2) return 'cheerful';\n  if (longBrowsing > quickDecisions && backAndForth > 3) return 'indecisive';\n  if (backAndForth > 5) return 'frustrated';\n\n  return 'focused';\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,MAAc;IACzC,OAAO,OAAO,OAAO,CAAC;AACxB;AAEO,SAAS,kBAAkB,MAAmC;IACnE,MAAM,SAAS;QACb,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IACA,OAAO,MAAM,CAAC,OAAO;AACvB;AAEO,SAAS,iBAAiB,MAAmC;IAClE,MAAM,QAAQ;QACZ,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IACA,OAAO,KAAK,CAAC,OAAO;AACtB;AAEO,SAAS,yBAAyB,MAAc,EAAE,QAAgB;IACvE,iFAAiF;IACjF,MAAM,iBAAiB,QAAQ,oCAAoC;IACnE,OAAO,SAAS,WAAW;AAC7B;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,MAAM,CAAC,GAAG,aAAa;AACrC;AAEO,SAAS,mBAAmB,UAAkB;IAMnD,MAAM,MAAM,IAAI,OAAO,OAAO;IAC9B,MAAM,SAAS,IAAI,KAAK,YAAY,OAAO;IAC3C,MAAM,aAAa,SAAS;IAE5B,IAAI,cAAc,GAAG;QACnB,OAAO;YAAE,OAAO;YAAG,SAAS;YAAG,SAAS;YAAG,SAAS;QAAK;IAC3D;IAEA,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa,CAAC,OAAO,KAAK,EAAE;IACrD,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,aAAa,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IACvE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,aAAa,CAAC,OAAO,EAAE,IAAK;IAExD,OAAO;QAAE;QAAO;QAAS;QAAS,SAAS;IAAM;AACnD;AAEO,SAAS,aAAa,GAAW;IACtC,MAAM,SAAiC;QACrC,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;IACb;IAEA,OAAO,MAAM,CAAC,IAAI,WAAW,GAAG,IAAI;AACtC;AAEO,SAAS,gBAAgB,cAA0D;IACxF,kDAAkD;IAClD,sDAAsD;IAEtD,IAAI,eAAe,MAAM,KAAK,GAAG,OAAO;IAExC,MAAM,iBAAiB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,GAAG,IAAI,MAAM;IAC1E,MAAM,eAAe,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,GAAG,KAAK,MAAM;IACzE,MAAM,eAAe,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,mBAAmB,MAAM;IAEpF,IAAI,iBAAiB,gBAAgB,eAAe,GAAG,OAAO;IAC9D,IAAI,eAAe,kBAAkB,eAAe,GAAG,OAAO;IAC9D,IAAI,eAAe,GAAG,OAAO;IAE7B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'success';\n}\n\nfunction Badge({ className, variant = 'default', ...props }: BadgeProps) {\n  const variants = {\n    default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n    secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n    destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n    outline: \"text-foreground\",\n    success: \"border-transparent bg-green-500 text-white hover:bg-green-500/80\",\n  };\n\n  return (\n    <div\n      className={cn(\n        \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n        variants[variant],\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Badge };\n"], "names": [], "mappings": ";;;;AACA;;;AAOA,SAAS,MAAM,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAmB;IACrE,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,aAAa;QACb,SAAS;QACT,SAAS;IACX;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0KACA,QAAQ,CAAC,QAAQ,EACjB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/src/app/page.tsx"], "sourcesContent": ["import { ProductGrid } from \"@/components/product/product-grid\";\nimport { sampleProducts } from \"@/data/products\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Spark<PERSON>, Leaf, Zap } from \"lucide-react\";\n\nexport default function Home() {\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* Hero Section */}\n      <section className=\"text-center py-12 mb-12\">\n        <div className=\"max-w-3xl mx-auto\">\n          <h1 className=\"text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent\">\n            Welcome to NextGen Store\n          </h1>\n          <p className=\"text-xl text-muted-foreground mb-8\">\n            Experience the future of shopping with AI-powered recommendations,\n            sustainable choices, and personalized experiences.\n          </p>\n          <div className=\"flex flex-wrap justify-center gap-4 mb-8\">\n            <Badge variant=\"secondary\" className=\"px-4 py-2\">\n              <Sparkles className=\"w-4 h-4 mr-2\" />\n              AI-Powered\n            </Badge>\n            <Badge variant=\"secondary\" className=\"px-4 py-2\">\n              <Leaf className=\"w-4 h-4 mr-2\" />\n              Eco-Friendly\n            </Badge>\n            <Badge variant=\"secondary\" className=\"px-4 py-2\">\n              <Zap className=\"w-4 h-4 mr-2\" />\n              Smart Shopping\n            </Badge>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Products */}\n      <section>\n        <div className=\"flex items-center justify-between mb-8\">\n          <h2 className=\"text-3xl font-bold\">Featured Products</h2>\n          <p className=\"text-muted-foreground\">\n            Discover our curated selection of premium items\n          </p>\n        </div>\n\n        <ProductGrid products={sampleProducts} />\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-16 mt-16 border-t\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold mb-4\">Why Choose NextGen Store?</h2>\n          <p className=\"text-muted-foreground max-w-2xl mx-auto\">\n            We&apos;re not just another online store. We&apos;re pioneering the future of eCommerce\n            with cutting-edge technology and sustainable practices.\n          </p>\n        </div>\n\n        <div className=\"grid md:grid-cols-3 gap-8\">\n          <div className=\"text-center p-6\">\n            <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <Sparkles className=\"w-8 h-8 text-primary\" />\n            </div>\n            <h3 className=\"text-xl font-semibold mb-2\">AI Personal Shopper</h3>\n            <p className=\"text-muted-foreground\">\n              Our emotion-aware AI chatbot understands your mood and preferences\n              to recommend the perfect products for you.\n            </p>\n          </div>\n\n          <div className=\"text-center p-6\">\n            <div className=\"w-16 h-16 bg-green-500/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <Leaf className=\"w-8 h-8 text-green-500\" />\n            </div>\n            <h3 className=\"text-xl font-semibold mb-2\">Sustainable Shopping</h3>\n            <p className=\"text-muted-foreground\">\n              Every product shows its eco-rating and carbon footprint,\n              helping you make environmentally conscious choices.\n            </p>\n          </div>\n\n          <div className=\"text-center p-6\">\n            <div className=\"w-16 h-16 bg-blue-500/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <Zap className=\"w-8 h-8 text-blue-500\" />\n            </div>\n            <h3 className=\"text-xl font-semibold mb-2\">Smart Features</h3>\n            <p className=\"text-muted-foreground\">\n              From virtual try-on to personalized flash sales,\n              experience shopping like never before.\n            </p>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAgH;;;;;;sCAG9H,8OAAC;4BAAE,WAAU;sCAAqC;;;;;;sCAIlD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;sDACnC,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;sDACnC,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGnC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;sDACnC,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,8OAAC;;kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKvC,8OAAC,gJAAA,CAAA,cAAW;wBAAC,UAAU,uHAAA,CAAA,iBAAc;;;;;;;;;;;;0BAIvC,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAMvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAMvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD", "debugId": null}}, {"offset": {"line": 889, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/node_modules/clsx/dist/clsx.mjs"], "sourcesContent": ["function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;"], "names": [], "mappings": ";;;;AAAA,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE,GAAE,IAAE;IAAG,IAAG,YAAU,OAAO,KAAG,YAAU,OAAO,GAAE,KAAG;SAAO,IAAG,YAAU,OAAO,GAAE,IAAG,MAAM,OAAO,CAAC,IAAG;QAAC,IAAI,IAAE,EAAE,MAAM;QAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAC,OAAM,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;AAAQ,SAAS;IAAO,IAAI,IAAI,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,MAAM,EAAC,IAAE,GAAE,IAAI,CAAC,IAAE,SAAS,CAAC,EAAE,KAAG,CAAC,IAAE,EAAE,EAAE,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;uCAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,oBAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAE,WAAY,CAAA,CAAA;AAQ/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,CAAA,CAAmB,MAAA,CAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAyB,CAAC,OAAO,CAAI,CAAA,CAAA,CAAA,EAAA,CAClD,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,GAAA,CAAA,CAAA,CAAG,WAAY,CAAA;AAS9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA;IACvE,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;IAE5B,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,MAAA,CAAO,CAAC,CAAA,CAAE,WAAA,EAAgB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAM,CAAC,CAAA;AAC/D,CAAA;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,GAAG,CAAA,CACR,IAAK,CAAA,CAAA;AAQG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA;IACzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,QAAQ,KAAO,CAAA;QACxB,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,OAAS,CAAA,CAAA;YAC5D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA;QACT;IACF;AACF,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "file": "defaultAttributes.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 976, "column": 0}, "map": {"version": 3, "file": "Icon.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACX,CACE,EACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,EACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,EACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,EACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,GAAG,CAAA,CAAA,CAAA,GAAA,EAEL,CAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,+MAAA,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,EACA;QACE,CAAA,CAAA,CAAA;QACA,uKAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,8KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA;QAC3C,CAAI,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAC,cAAA,EAAY,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,MAAO;QAAA,CAAA;QAC/D,GAAG,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,EACA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA;WACvD,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW;YAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;SAAA;KACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1016, "column": 0}, "map": {"version": 3, "file": "createLucideIcon.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,yMACjF,gBAAA,yJAAc,UAAM,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,+KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACT,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,+KAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAA,CAAA,EAC7C,CAAA,OAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,EAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CACJ;IAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAc,eAAA,EAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;IAEtC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA;AACT,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1048, "column": 0}, "map": {"version": 3, "file": "sparkles.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/node_modules/lucide-react/src/icons/sparkles.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z',\n      key: '4pj2yx',\n    },\n  ],\n  ['path', { d: 'M20 3v4', key: '1olli1' }],\n  ['path', { d: 'M22 5h-4', key: '1gvqau' }],\n  ['path', { d: 'M4 17v2', key: 'vumght' }],\n  ['path', { d: 'M5 18H3', key: 'zchphs' }],\n];\n\n/**\n * @component @name Sparkles\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOS45MzcgMTUuNUEyIDIgMCAwIDAgOC41IDE0LjA2M2wtNi4xMzUtMS41ODJhLjUuNSAwIDAgMSAwLS45NjJMOC41IDkuOTM2QTIgMiAwIDAgMCA5LjkzNyA4LjVsMS41ODItNi4xMzVhLjUuNSAwIDAgMSAuOTYzIDBMMTQuMDYzIDguNUEyIDIgMCAwIDAgMTUuNSA5LjkzN2w2LjEzNSAxLjU4MWEuNS41IDAgMCAxIDAgLjk2NEwxNS41IDE0LjA2M2EyIDIgMCAwIDAtMS40MzcgMS40MzdsLTEuNTgyIDYuMTM1YS41LjUgMCAwIDEtLjk2MyAweiIgLz4KICA8cGF0aCBkPSJNMjAgM3Y0IiAvPgogIDxwYXRoIGQ9Ik0yMiA1aC00IiAvPgogIDxwYXRoIGQ9Ik00IDE3djIiIC8+CiAgPHBhdGggZD0iTTUgMThIMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/sparkles\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Sparkles = createLucideIcon('sparkles', __iconNode);\n\nexport default Sparkles;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC1C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1115, "column": 0}, "map": {"version": 3, "file": "leaf.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/node_modules/lucide-react/src/icons/leaf.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z',\n      key: 'nnexq3',\n    },\n  ],\n  ['path', { d: 'M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12', key: 'mt58a7' }],\n];\n\n/**\n * @component @name Leaf\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEgMjBBNyA3IDAgMCAxIDkuOCA2LjFDMTUuNSA1IDE3IDQuNDggMTkgMmMxIDIgMiA0LjE4IDIgOCAwIDUuNS00Ljc4IDEwLTEwIDEwWiIgLz4KICA8cGF0aCBkPSJNMiAyMWMwLTMgMS44NS01LjM2IDUuMDgtNkM5LjUgMTQuNTIgMTIgMTMgMTMgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/leaf\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Leaf = createLucideIcon('leaf', __iconNode);\n\nexport default Leaf;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAoD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACnF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1161, "column": 0}, "map": {"version": 3, "file": "zap.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/node_modules/lucide-react/src/icons/zap.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z',\n      key: '1xq2db',\n    },\n  ],\n];\n\n/**\n * @component @name Zap\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxNGExIDEgMCAwIDEtLjc4LTEuNjNsOS45LTEwLjJhLjUuNSAwIDAgMSAuODYuNDZsLTEuOTIgNi4wMkExIDEgMCAwIDAgMTMgMTBoN2ExIDEgMCAwIDEgLjc4IDEuNjNsLTkuOSAxMC4yYS41LjUgMCAwIDEtLjg2LS40NmwxLjkyLTYuMDJBMSAxIDAgMCAwIDExIDE0eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/zap\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Zap = createLucideIcon('zap', __iconNode);\n\nexport default Zap;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;CACF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1237, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AI%20Internship/e-commerence/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,CAA8C,EAAtB,AAAuB;YAAA;gBAEzG,UAAA,CAAA;gBAAA,QAAA;oBAAA,IAAA,0BAA4D;oBAAA;iBAAA;YAC5D;SAAA,KAAO,MAAMC,cAAc,IAAIX,mBAAmB;;KAChDY,YAAY;cACVC,IAAAA,EAAMZ;YAAAA,MAAAA,CAAUa,QAAQ;iBACxBC,MAAM,QAAA;wBAAA;4BACNC,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,KAAU,iBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACV,OAAA,GAAA,6SAAA,CAAA,UAAA,CAAA,KAAA,CAA2C,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BAC3CC,MAAAA,CAAAA,KAAY,OAAA,CAAA;;qBACZC,UAAU;gBACVC,UAAU,EAAE;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,CAAA;YAAA,IAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;IACF;CAAA,CAAE", "ignoreList": [0], "debugId": null}}]}