import { Product } from '@/types';

export const sampleProducts: Product[] = [
  {
    id: '1',
    name: 'Eco-Friendly Cotton T-Shirt',
    price: 29.99,
    images: [
      'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=600&fit=crop',
      'https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=500&h=600&fit=crop',
      'https://images.unsplash.com/photo-1562157873-818bc0726f68?w=500&h=600&fit=crop'
    ],
    colors: [
      {
        name: 'Black',
        value: '#000000',
        image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=500&h=600&fit=crop'
      },
      {
        name: 'White',
        value: '#FFFFFF',
        image: 'https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=500&h=600&fit=crop'
      },
      {
        name: 'Navy',
        value: '#000080',
        image: 'https://images.unsplash.com/photo-1562157873-818bc0726f68?w=500&h=600&fit=crop'
      }
    ],
    sizes: ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
    description: 'Made from 100% organic cotton, this comfortable t-shirt is perfect for everyday wear. Sustainably sourced and ethically manufactured.',
    category: 'Clothing',
    inStock: true,
    rating: 4.5,
    reviews: 128,
    ecoRating: 'A',
    carbonFootprint: 2.1,
    brand: 'EcoWear',
    material: '100% Organic Cotton',
    careInstructions: ['Machine wash cold', 'Tumble dry low', 'Do not bleach'],
    tags: ['sustainable', 'organic', 'basic', 'casual']
  },
  {
    id: '2',
    name: 'Premium Denim Jeans',
    price: 89.99,
    images: [
      'https://images.unsplash.com/photo-1542272604-787c3835535d?w=500&h=600&fit=crop',
      'https://images.unsplash.com/photo-1475178626620-a4d074967452?w=500&h=600&fit=crop',
      'https://images.unsplash.com/photo-1506629905607-d9b1e5b6e8b7?w=500&h=600&fit=crop'
    ],
    colors: [
      {
        name: 'Dark Blue',
        value: '#1e3a8a',
        image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=500&h=600&fit=crop'
      },
      {
        name: 'Light Blue',
        value: '#3b82f6',
        image: 'https://images.unsplash.com/photo-1475178626620-a4d074967452?w=500&h=600&fit=crop'
      },
      {
        name: 'Black',
        value: '#000000',
        image: 'https://images.unsplash.com/photo-1506629905607-d9b1e5b6e8b7?w=500&h=600&fit=crop'
      }
    ],
    sizes: ['28', '30', '32', '34', '36', '38', '40'],
    description: 'Classic fit denim jeans made with premium cotton blend. Features a comfortable mid-rise waist and timeless straight-leg cut.',
    category: 'Clothing',
    inStock: true,
    rating: 4.3,
    reviews: 89,
    ecoRating: 'C',
    carbonFootprint: 8.5,
    brand: 'DenimCo',
    material: '98% Cotton, 2% Elastane',
    careInstructions: ['Machine wash cold', 'Hang dry', 'Iron on medium heat'],
    tags: ['denim', 'classic', 'versatile']
  },
  {
    id: '3',
    name: 'Wireless Bluetooth Headphones',
    price: 199.99,
    images: [
      'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500&h=600&fit=crop',
      'https://images.unsplash.com/photo-1484704849700-f032a568e944?w=500&h=600&fit=crop',
      'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=500&h=600&fit=crop'
    ],
    colors: [
      {
        name: 'Black',
        value: '#000000',
        image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500&h=600&fit=crop'
      },
      {
        name: 'White',
        value: '#FFFFFF',
        image: 'https://images.unsplash.com/photo-1484704849700-f032a568e944?w=500&h=600&fit=crop'
      },
      {
        name: 'Silver',
        value: '#C0C0C0',
        image: 'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=500&h=600&fit=crop'
      }
    ],
    sizes: ['One Size'],
    description: 'Premium wireless headphones with active noise cancellation, 30-hour battery life, and crystal-clear audio quality.',
    category: 'Electronics',
    inStock: true,
    rating: 4.7,
    reviews: 256,
    ecoRating: 'B',
    carbonFootprint: 12.3,
    brand: 'AudioTech',
    material: 'Plastic, Metal, Foam',
    careInstructions: ['Clean with dry cloth', 'Store in case when not in use'],
    tags: ['wireless', 'noise-cancelling', 'premium', 'audio']
  },
  {
    id: '4',
    name: 'Minimalist Leather Wallet',
    price: 49.99,
    images: [
      'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=500&h=600&fit=crop',
      'https://images.unsplash.com/photo-1627123424574-724758594e93?w=500&h=600&fit=crop',
      'https://images.unsplash.com/photo-1608686207856-001b95cf60ca?w=500&h=600&fit=crop'
    ],
    colors: [
      {
        name: 'Brown',
        value: '#8B4513',
        image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=500&h=600&fit=crop'
      },
      {
        name: 'Black',
        value: '#000000',
        image: 'https://images.unsplash.com/photo-1627123424574-724758594e93?w=500&h=600&fit=crop'
      },
      {
        name: 'Tan',
        value: '#D2B48C',
        image: 'https://images.unsplash.com/photo-1608686207856-001b95cf60ca?w=500&h=600&fit=crop'
      }
    ],
    sizes: ['One Size'],
    description: 'Handcrafted genuine leather wallet with RFID blocking technology. Slim design holds up to 8 cards and cash.',
    category: 'Accessories',
    inStock: true,
    rating: 4.4,
    reviews: 67,
    ecoRating: 'B',
    carbonFootprint: 3.2,
    brand: 'LeatherCraft',
    material: 'Genuine Leather',
    careInstructions: ['Clean with leather conditioner', 'Keep away from water'],
    tags: ['leather', 'minimalist', 'RFID', 'handcrafted']
  }
];

export const categories = [
  'All',
  'Clothing',
  'Electronics',
  'Accessories',
  'Home & Garden',
  'Sports & Outdoors',
  'Beauty & Personal Care'
];

export const brands = [
  'EcoWear',
  'DenimCo',
  'AudioTech',
  'LeatherCraft',
  'TechGear',
  'StyleCo',
  'GreenLiving'
];

export const colors = [
  { name: 'Black', value: '#000000' },
  { name: 'White', value: '#FFFFFF' },
  { name: 'Red', value: '#FF0000' },
  { name: 'Blue', value: '#0000FF' },
  { name: 'Green', value: '#008000' },
  { name: 'Yellow', value: '#FFFF00' },
  { name: 'Purple', value: '#800080' },
  { name: 'Orange', value: '#FFA500' },
  { name: 'Pink', value: '#FFC0CB' },
  { name: 'Brown', value: '#8B4513' },
  { name: 'Gray', value: '#808080' },
  { name: 'Navy', value: '#000080' }
];

export const sizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL', '28', '30', '32', '34', '36', '38', '40', 'One Size'];
