'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Star, Heart, Leaf } from 'lucide-react';
import { Product } from '@/types';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { formatPrice, getEcoRatingColor } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface ProductCardProps {
  product: Product;
  onQuickAdd?: (product: Product) => void;
  onViewDetails?: (product: Product) => void;
}

export function ProductCard({ product, onQuickAdd, onViewDetails }: ProductCardProps) {
  const [currentImage, setCurrentImage] = useState(product.images[0]);
  const [hoveredColor, setHoveredColor] = useState<string | null>(null);
  const [isLiked, setIsLiked] = useState(false);

  const handleColorHover = (colorImage: string) => {
    setCurrentImage(colorImage);
    setHoveredColor(colorImage);
  };

  const handleColorLeave = () => {
    if (!hoveredColor) return;
    setCurrentImage(product.images[0]);
    setHoveredColor(null);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={cn(
          "w-4 h-4",
          i < Math.floor(rating)
            ? "fill-yellow-400 text-yellow-400"
            : "text-gray-300"
        )}
      />
    ));
  };

  return (
    <Card className="group relative overflow-hidden transition-all duration-300 hover:shadow-lg">
      {/* Wishlist Button */}
      <Button
        variant="ghost"
        size="icon"
        className="absolute top-2 right-2 z-10 bg-white/80 backdrop-blur-sm hover:bg-white"
        onClick={() => setIsLiked(!isLiked)}
      >
        <Heart
          className={cn(
            "w-4 h-4 transition-colors",
            isLiked ? "fill-red-500 text-red-500" : "text-gray-600"
          )}
        />
      </Button>

      {/* Eco Rating Badge */}
      {product.ecoRating && (
        <Badge
          className={cn(
            "absolute top-2 left-2 z-10 text-white",
            getEcoRatingColor(product.ecoRating)
          )}
        >
          <Leaf className="w-3 h-3 mr-1" />
          {product.ecoRating}
        </Badge>
      )}

      {/* Product Image */}
      <div className="relative aspect-square overflow-hidden">
        <Image
          src={currentImage}
          alt={product.name}
          fill
          className="object-cover transition-transform duration-300 group-hover:scale-105"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />

        {/* Quick Add Button - appears on hover */}
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center p-4">
          <Button
            onClick={() => onQuickAdd?.(product)}
            className="w-full bg-white text-black hover:bg-gray-100"
          >
            Quick Add
          </Button>
        </div>
      </div>

      <CardContent className="p-4">
        {/* Brand */}
        {product.brand && (
          <p className="text-xs text-muted-foreground uppercase tracking-wide mb-1">
            {product.brand}
          </p>
        )}

        {/* Product Name */}
        <h3
          className="font-semibold text-sm mb-2 line-clamp-2 cursor-pointer hover:text-primary transition-colors"
          onClick={() => onViewDetails?.(product)}
        >
          {product.name}
        </h3>

        {/* Rating */}
        <div className="flex items-center gap-1 mb-2">
          <div className="flex">
            {renderStars(product.rating)}
          </div>
          <span className="text-xs text-muted-foreground ml-1">
            ({product.reviews})
          </span>
        </div>

        {/* Price */}
        <div className="flex items-center justify-between mb-3">
          <span className="text-lg font-bold">
            {formatPrice(product.price)}
          </span>
          {product.carbonFootprint && (
            <span className="text-xs text-muted-foreground">
              {product.carbonFootprint}kg CO₂
            </span>
          )}
        </div>

        {/* Color Swatches */}
        <div className="flex gap-2 mb-3">
          {product.colors.map((color, index) => (
            <button
              key={index}
              type="button"
              className={cn(
                "w-6 h-6 rounded-full border-2 transition-all duration-200",
                hoveredColor === color.image
                  ? "border-primary scale-110"
                  : "border-gray-300 hover:border-gray-400"
              )}
              style={{ backgroundColor: color.value }}
              onMouseEnter={() => handleColorHover(color.image)}
              onMouseLeave={handleColorLeave}
              title={color.name}
            />
          ))}
        </div>

        {/* Available Sizes */}
        <div className="flex flex-wrap gap-1">
          {product.sizes.slice(0, 4).map((size, index) => (
            <Badge key={index} variant="outline" className="text-xs px-2 py-1">
              {size}
            </Badge>
          ))}
          {product.sizes.length > 4 && (
            <Badge variant="outline" className="text-xs px-2 py-1">
              +{product.sizes.length - 4}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
